{"name": "@autopiloot/cms", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=\"--no-deprecation --max-old-space-size=8000\" next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "test": "pnpm run test:int && pnpm run test:e2e", "test:e2e": "cross-env NODE_OPTIONS=\"--no-deprecation --no-experimental-strip-types\" pnpm exec playwright test", "test:int": "cross-env NODE_OPTIONS=--no-deprecation vitest run --config ./vitest.config.mts"}, "dependencies": {"@payloadcms/db-postgres": "3.52.0", "@payloadcms/next": "3.52.0", "@payloadcms/payload-cloud": "3.52.0", "@payloadcms/plugin-seo": "^3.52.0", "@payloadcms/richtext-lexical": "3.52.0", "@payloadcms/ui": "3.52.0", "cross-env": "^10.0.0", "dotenv": "17.2.1", "graphql": "^16.11.0", "next": "15.5.0", "payload": "3.52.0", "payload-oapi": "^0.2.4", "react": "19.1.1", "sharp": "0.34.2"}, "devDependencies": {"@payloadcms/admin-bar": "3.52.0", "@playwright/test": "1.55.0", "@testing-library/react": "16.3.0", "@autopiloot/eslint-config": "workspace:*", "@autopiloot/typescript-config": "workspace:*", "@types/node": "^24.3.0", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "@vitejs/plugin-react": "5.0.1", "eslint": "^9.33.0", "eslint-config-next": "15.5.0", "jsdom": "26.1.0", "playwright": "1.55.0", "playwright-core": "1.55.0", "prettier": "^3.6.2", "typescript": "5.9.2", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.4"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp", "esbuild", "unrs-resolver"]}}
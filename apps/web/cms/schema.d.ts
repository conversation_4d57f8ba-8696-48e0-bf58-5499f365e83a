/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Users */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "name" | "-name" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt" | "apiKey" | "-apiKey" | "apiKeyIndex" | "-apiKeyIndex" | "email" | "-email" | "resetPasswordToken" | "-resetPasswordToken" | "resetPasswordExpiration" | "-resetPasswordExpiration" | "salt" | "-salt" | "hash" | "-hash" | "loginAttempts" | "-loginAttempts" | "lockUntil" | "-lockUntil";
                    where?: Record<string, never> & (components["schemas"]["UserQueryOperations"] | components["schemas"]["UserQueryOperationsAnd"] | components["schemas"]["UserQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["UserListResponse"];
            };
        };
        put?: never;
        /** Create a new User */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["UserRequestBody"];
            responses: {
                201: components["responses"]["NewUserResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the User */
                id: string;
            };
            cookie?: never;
        };
        /** Find a User by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the User */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["UserResponse"];
                404: components["responses"]["UserNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a User */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the User */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["UserResponse"];
                404: components["responses"]["UserNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a User */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the User */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["UserResponse"];
                404: components["responses"]["UserNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/media": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Media */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "alt" | "-alt" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt" | "url" | "-url" | "thumbnailURL" | "-thumbnailURL" | "filename" | "-filename" | "mimeType" | "-mimeType" | "filesize" | "-filesize" | "width" | "-width" | "height" | "-height" | "focalX" | "-focalX" | "focalY" | "-focalY";
                    where?: Record<string, never> & (components["schemas"]["MediaQueryOperations"] | components["schemas"]["MediaQueryOperationsAnd"] | components["schemas"]["MediaQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["MediaListResponse"];
            };
        };
        put?: never;
        /** Create a new Media */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["MediaRequestBody"];
            responses: {
                201: components["responses"]["NewMediaResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/media/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Media */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Media by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Media */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["MediaResponse"];
                404: components["responses"]["MediaNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Media */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Media */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["MediaResponse"];
                404: components["responses"]["MediaNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Media */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Media */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["MediaResponse"];
                404: components["responses"]["MediaNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/pages": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Pages */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "title" | "-title" | "publishedAt" | "-publishedAt" | "slug" | "-slug" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt";
                    where?: Record<string, never> & (components["schemas"]["PageQueryOperations"] | components["schemas"]["PageQueryOperationsAnd"] | components["schemas"]["PageQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PageListResponse"];
            };
        };
        put?: never;
        /** Create a new Page */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["PageRequestBody"];
            responses: {
                201: components["responses"]["NewPageResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/pages/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Page */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Page by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Page */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PageResponse"];
                404: components["responses"]["PageNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Page */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Page */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PageResponse"];
                404: components["responses"]["PageNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Page */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Page */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PageResponse"];
                404: components["responses"]["PageNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/posts": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Posts */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "title" | "-title" | "publishedAt" | "-publishedAt" | "slug" | "-slug" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt";
                    where?: Record<string, never> & (components["schemas"]["PostQueryOperations"] | components["schemas"]["PostQueryOperationsAnd"] | components["schemas"]["PostQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PostListResponse"];
            };
        };
        put?: never;
        /** Create a new Post */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["PostRequestBody"];
            responses: {
                201: components["responses"]["NewPostResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/posts/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Post */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Post by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Post */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PostResponse"];
                404: components["responses"]["PostNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Post */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Post */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PostResponse"];
                404: components["responses"]["PostNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Post */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Post */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PostResponse"];
                404: components["responses"]["PostNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/categories": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Categories */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "title" | "-title" | "slug" | "-slug" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt";
                    where?: Record<string, never> & (components["schemas"]["CategoryQueryOperations"] | components["schemas"]["CategoryQueryOperationsAnd"] | components["schemas"]["CategoryQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["CategoryListResponse"];
            };
        };
        put?: never;
        /** Create a new Category */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["CategoryRequestBody"];
            responses: {
                201: components["responses"]["NewCategoryResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/categories/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Category */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Category by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Category */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["CategoryResponse"];
                404: components["responses"]["CategoryNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Category */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Category */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["CategoryResponse"];
                404: components["responses"]["CategoryNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Category */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Category */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["CategoryResponse"];
                404: components["responses"]["CategoryNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/payload-jobs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Payload Jobs */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "queue" | "-queue" | "waitUntil" | "-waitUntil" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt";
                    where?: Record<string, never> & (components["schemas"]["PayloadJobQueryOperations"] | components["schemas"]["PayloadJobQueryOperationsAnd"] | components["schemas"]["PayloadJobQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadJobListResponse"];
            };
        };
        put?: never;
        /** Create a new Payload Job */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["PayloadJobRequestBody"];
            responses: {
                201: components["responses"]["NewPayloadJobResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/payload-jobs/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Payload Job */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Payload Job by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Job */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadJobResponse"];
                404: components["responses"]["PayloadJobNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Payload Job */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Job */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadJobResponse"];
                404: components["responses"]["PayloadJobNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Payload Job */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Job */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadJobResponse"];
                404: components["responses"]["PayloadJobNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/payload-locked-documents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Payload Locked Documents */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "globalSlug" | "-globalSlug" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt";
                    where?: Record<string, never> & (components["schemas"]["PayloadLockedDocumentQueryOperations"] | components["schemas"]["PayloadLockedDocumentQueryOperationsAnd"] | components["schemas"]["PayloadLockedDocumentQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadLockedDocumentListResponse"];
            };
        };
        put?: never;
        /** Create a new Payload Locked Document */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["PayloadLockedDocumentRequestBody"];
            responses: {
                201: components["responses"]["NewPayloadLockedDocumentResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/payload-locked-documents/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Payload Locked Document */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Payload Locked Document by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Locked Document */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadLockedDocumentResponse"];
                404: components["responses"]["PayloadLockedDocumentNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Payload Locked Document */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Locked Document */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadLockedDocumentResponse"];
                404: components["responses"]["PayloadLockedDocumentNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Payload Locked Document */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Locked Document */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadLockedDocumentResponse"];
                404: components["responses"]["PayloadLockedDocumentNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/payload-preferences": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Payload Preferences */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "key" | "-key" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt";
                    where?: Record<string, never> & (components["schemas"]["PayloadPreferenceQueryOperations"] | components["schemas"]["PayloadPreferenceQueryOperationsAnd"] | components["schemas"]["PayloadPreferenceQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadPreferenceListResponse"];
            };
        };
        put?: never;
        /** Create a new Payload Preference */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["PayloadPreferenceRequestBody"];
            responses: {
                201: components["responses"]["NewPayloadPreferenceResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/payload-preferences/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Payload Preference */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Payload Preference by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Preference */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadPreferenceResponse"];
                404: components["responses"]["PayloadPreferenceNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Payload Preference */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Preference */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadPreferenceResponse"];
                404: components["responses"]["PayloadPreferenceNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Payload Preference */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Preference */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadPreferenceResponse"];
                404: components["responses"]["PayloadPreferenceNotFoundResponse"];
            };
        };
        trace?: never;
    };
    "/api/payload-migrations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Retrieve a list of Payload Migrations */
        get: {
            parameters: {
                query?: {
                    page?: number;
                    limit?: number;
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                    sort?: "name" | "-name" | "batch" | "-batch" | "updatedAt" | "-updatedAt" | "createdAt" | "-createdAt";
                    where?: Record<string, never> & (components["schemas"]["PayloadMigrationQueryOperations"] | components["schemas"]["PayloadMigrationQueryOperationsAnd"] | components["schemas"]["PayloadMigrationQueryOperationsOr"]);
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadMigrationListResponse"];
            };
        };
        put?: never;
        /** Create a new Payload Migration */
        post: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: components["requestBodies"]["PayloadMigrationRequestBody"];
            responses: {
                201: components["responses"]["NewPayloadMigrationResponse"];
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/payload-migrations/{id}": {
        parameters: {
            query?: {
                depth?: number;
                locale?: string;
                "fallback-locale"?: string;
            };
            header?: never;
            path: {
                /** @description ID of the Payload Migration */
                id: string;
            };
            cookie?: never;
        };
        /** Find a Payload Migration by ID */
        get: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Migration */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadMigrationResponse"];
                404: components["responses"]["PayloadMigrationNotFoundResponse"];
            };
        };
        put?: never;
        post?: never;
        /** Delete a Payload Migration */
        delete: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Migration */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadMigrationResponse"];
                404: components["responses"]["PayloadMigrationNotFoundResponse"];
            };
        };
        options?: never;
        head?: never;
        /** Update a Payload Migration */
        patch: {
            parameters: {
                query?: {
                    depth?: number;
                    locale?: string;
                    "fallback-locale"?: string;
                };
                header?: never;
                path: {
                    /** @description ID of the Payload Migration */
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                200: components["responses"]["PayloadMigrationResponse"];
                404: components["responses"]["PayloadMigrationNotFoundResponse"];
            };
        };
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** @example Europe/Prague */
        supportedTimezones: string;
        /** User */
        User: {
            id: string;
            name?: string | null;
            updatedAt: string;
            createdAt: string;
            enableAPIKey?: boolean | null;
            apiKey?: string | null;
            apiKeyIndex?: string | null;
            email: string;
            resetPasswordToken?: string | null;
            resetPasswordExpiration?: string | null;
            salt?: string | null;
            hash?: string | null;
            loginAttempts?: number | null;
            lockUntil?: string | null;
            sessions?: {
                id: string;
                createdAt?: string | null;
                expiresAt: string;
            }[] | null;
            password?: string | null;
        };
        /** Media */
        Media: {
            id: string;
            alt?: string | null;
            caption?: {
                root: {
                    type: string;
                    children: ({
                        type: string;
                        version: number;
                    } & {
                        [key: string]: unknown;
                    })[];
                    direction: "ltr" | "rtl" | null;
                    /** @enum {string} */
                    format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                    indent: number;
                    version: number;
                };
            } | null;
            updatedAt: string;
            createdAt: string;
            url?: string | null;
            thumbnailURL?: string | null;
            filename?: string | null;
            mimeType?: string | null;
            filesize?: number | null;
            width?: number | null;
            height?: number | null;
            focalX?: number | null;
            focalY?: number | null;
            sizes?: {
                thumbnail?: {
                    url?: string | null;
                    width?: number | null;
                    height?: number | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    filename?: string | null;
                };
                square?: {
                    url?: string | null;
                    width?: number | null;
                    height?: number | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    filename?: string | null;
                };
                small?: {
                    url?: string | null;
                    width?: number | null;
                    height?: number | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    filename?: string | null;
                };
                medium?: {
                    url?: string | null;
                    width?: number | null;
                    height?: number | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    filename?: string | null;
                };
                large?: {
                    url?: string | null;
                    width?: number | null;
                    height?: number | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    filename?: string | null;
                };
                xlarge?: {
                    url?: string | null;
                    width?: number | null;
                    height?: number | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    filename?: string | null;
                };
                og?: {
                    url?: string | null;
                    width?: number | null;
                    height?: number | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    filename?: string | null;
                };
            };
        };
        /** Page */
        Page: {
            id: string;
            title: string;
            layout: ({
                richText?: {
                    root: {
                        type: string;
                        children: ({
                            type: string;
                            version: number;
                        } & {
                            [key: string]: unknown;
                        })[];
                        direction: "ltr" | "rtl" | null;
                        /** @enum {string} */
                        format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                        indent: number;
                        version: number;
                    };
                } | null;
                links?: {
                    link: {
                        /** @enum {string|null} */
                        type?: "reference" | "custom" | null;
                        newTab?: boolean | null;
                        reference?: ({
                            /** @enum {unknown} */
                            relationTo: "pages";
                            value: string | components["schemas"]["Page"];
                        } | null) | ({
                            /** @enum {unknown} */
                            relationTo: "posts";
                            value: string | components["schemas"]["Post"];
                        } | null);
                        url?: string | null;
                        label: string;
                        /**
                         * @description Choose how the link should be rendered.
                         * @enum {string|null}
                         */
                        appearance?: "default" | "outline" | null;
                    };
                    id?: string | null;
                }[] | null;
                id?: string | null;
                blockName?: string | null;
                /** @enum {unknown} */
                blockType: "cta";
            } | {
                columns?: {
                    /** @enum {string|null} */
                    size?: "oneThird" | "half" | "twoThirds" | "full" | null;
                    richText?: {
                        root: {
                            type: string;
                            children: ({
                                type: string;
                                version: number;
                            } & {
                                [key: string]: unknown;
                            })[];
                            direction: "ltr" | "rtl" | null;
                            /** @enum {string} */
                            format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                            indent: number;
                            version: number;
                        };
                    } | null;
                    enableLink?: boolean | null;
                    link?: {
                        /** @enum {string|null} */
                        type?: "reference" | "custom" | null;
                        newTab?: boolean | null;
                        reference?: ({
                            /** @enum {unknown} */
                            relationTo: "pages";
                            value: string | components["schemas"]["Page"];
                        } | null) | ({
                            /** @enum {unknown} */
                            relationTo: "posts";
                            value: string | components["schemas"]["Post"];
                        } | null);
                        url?: string | null;
                        label: string;
                        /**
                         * @description Choose how the link should be rendered.
                         * @enum {string|null}
                         */
                        appearance?: "default" | "outline" | null;
                    };
                    id?: string | null;
                }[] | null;
                id?: string | null;
                blockName?: string | null;
                /** @enum {unknown} */
                blockType: "content";
            } | {
                media: string | components["schemas"]["Media"];
                id?: string | null;
                blockName?: string | null;
                /** @enum {unknown} */
                blockType: "mediaBlock";
            } | {
                introContent?: {
                    root: {
                        type: string;
                        children: ({
                            type: string;
                            version: number;
                        } & {
                            [key: string]: unknown;
                        })[];
                        direction: "ltr" | "rtl" | null;
                        /** @enum {string} */
                        format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                        indent: number;
                        version: number;
                    };
                } | null;
                /** @enum {string|null} */
                populateBy?: "collection" | "selection" | null;
                /** @enum {string|null} */
                relationTo?: "posts" | null;
                categories?: (string | components["schemas"]["Category"])[] | null;
                limit?: number | null;
                selectedDocs?: {
                    /** @enum {unknown} */
                    relationTo: "posts";
                    value: string | components["schemas"]["Post"];
                }[] | null;
                id?: string | null;
                blockName?: string | null;
                /** @enum {unknown} */
                blockType: "archive";
            })[];
            meta?: {
                title?: string | null;
                /** @description Maximum upload file size: 12MB. Recommended file size for images is <500KB. */
                image?: (string | null) | components["schemas"]["Media"];
                description?: string | null;
            };
            publishedAt?: string | null;
            slug?: string | null;
            slugLock?: boolean | null;
            updatedAt: string;
            createdAt: string;
            /** @enum {string|null} */
            _status?: "draft" | "published" | null;
        };
        /** Post */
        Post: {
            id: string;
            title: string;
            heroImage?: (string | null) | components["schemas"]["Media"];
            content: {
                root: {
                    type: string;
                    children: ({
                        type: string;
                        version: number;
                    } & {
                        [key: string]: unknown;
                    })[];
                    direction: "ltr" | "rtl" | null;
                    /** @enum {string} */
                    format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                    indent: number;
                    version: number;
                };
            };
            relatedPosts?: (string | components["schemas"]["Post"])[] | null;
            categories?: (string | components["schemas"]["Category"])[] | null;
            meta?: {
                title?: string | null;
                /** @description Maximum upload file size: 12MB. Recommended file size for images is <500KB. */
                image?: (string | null) | components["schemas"]["Media"];
                description?: string | null;
            };
            publishedAt?: string | null;
            authors?: (string | components["schemas"]["User"])[] | null;
            populatedAuthors?: {
                id?: string | null;
                name?: string | null;
            }[] | null;
            slug?: string | null;
            slugLock?: boolean | null;
            updatedAt: string;
            createdAt: string;
            /** @enum {string|null} */
            _status?: "draft" | "published" | null;
        };
        /** Category */
        Category: {
            id: string;
            title: string;
            slug?: string | null;
            slugLock?: boolean | null;
            updatedAt: string;
            createdAt: string;
        };
        /** Payload Job */
        PayloadJob: {
            id: string;
            /** @description Input data provided to the job */
            input?: (Record<string, never> | unknown[] | string | number | boolean) | null;
            taskStatus?: (Record<string, never> | unknown[] | string | number | boolean) | null;
            completedAt?: string | null;
            totalTried?: number | null;
            /** @description If hasError is true this job will not be retried */
            hasError?: boolean | null;
            /** @description If hasError is true, this is the error that caused it */
            error?: (Record<string, never> | unknown[] | string | number | boolean) | null;
            /** @description Task execution log */
            log?: {
                executedAt: string;
                completedAt: string;
                /** @enum {string} */
                taskSlug: "inline" | "schedulePublish";
                taskID: string;
                input?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                output?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                /** @enum {string} */
                state: "failed" | "succeeded";
                error?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                id?: string | null;
            }[] | null;
            /** @enum {string|null} */
            taskSlug?: "inline" | "schedulePublish" | null;
            queue?: string | null;
            waitUntil?: string | null;
            processing?: boolean | null;
            updatedAt: string;
            createdAt: string;
        };
        /** Payload Locked Document */
        PayloadLockedDocument: {
            id: string;
            document?: ({
                /** @enum {unknown} */
                relationTo: "users";
                value: string | components["schemas"]["User"];
            } | null) | ({
                /** @enum {unknown} */
                relationTo: "media";
                value: string | components["schemas"]["Media"];
            } | null) | ({
                /** @enum {unknown} */
                relationTo: "pages";
                value: string | components["schemas"]["Page"];
            } | null) | ({
                /** @enum {unknown} */
                relationTo: "posts";
                value: string | components["schemas"]["Post"];
            } | null) | ({
                /** @enum {unknown} */
                relationTo: "categories";
                value: string | components["schemas"]["Category"];
            } | null) | ({
                /** @enum {unknown} */
                relationTo: "payload-jobs";
                value: string | components["schemas"]["PayloadJob"];
            } | null);
            globalSlug?: string | null;
            user: {
                /** @enum {unknown} */
                relationTo: "users";
                value: string | components["schemas"]["User"];
            };
            updatedAt: string;
            createdAt: string;
        };
        /** Payload Preference */
        PayloadPreference: {
            id: string;
            user: {
                /** @enum {unknown} */
                relationTo: "users";
                value: string | components["schemas"]["User"];
            };
            key?: string | null;
            value?: (Record<string, never> | unknown[] | string | number | boolean) | null;
            updatedAt: string;
            createdAt: string;
        };
        /** Payload Migration */
        PayloadMigration: {
            id: string;
            name?: string | null;
            batch?: number | null;
            updatedAt: string;
            createdAt: string;
        };
        /** User query operations */
        UserQueryOperations: {
            name?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            enableAPIKey?: {
                equals?: boolean;
                not_equals?: boolean;
                in?: string;
                not_in?: string;
            };
            apiKey?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            apiKeyIndex?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            email?: {
                /** Format: email */
                equals?: string;
                /** Format: email */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: email */
                contains?: string;
            };
            resetPasswordToken?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            resetPasswordExpiration?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            salt?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            hash?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            loginAttempts?: {
                equals?: number;
                not_equals?: number;
                in?: string;
                not_in?: string;
                greater_than?: number;
                greater_than_equal?: number;
                less_than?: number;
                less_than_equal?: number;
            };
            lockUntil?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
        };
        /** User query conjunction */
        UserQueryOperationsAnd: {
            and: (components["schemas"]["UserQueryOperations"] | components["schemas"]["UserQueryOperationsAnd"] | components["schemas"]["UserQueryOperationsOr"])[];
        };
        /** User query disjunction */
        UserQueryOperationsOr: {
            or: (components["schemas"]["UserQueryOperations"] | components["schemas"]["UserQueryOperationsAnd"] | components["schemas"]["UserQueryOperationsOr"])[];
        };
        /** Media query operations */
        MediaQueryOperations: {
            alt?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            url?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            thumbnailURL?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            filename?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            mimeType?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            filesize?: {
                equals?: number;
                not_equals?: number;
                in?: string;
                not_in?: string;
                greater_than?: number;
                greater_than_equal?: number;
                less_than?: number;
                less_than_equal?: number;
            };
            width?: {
                equals?: number;
                not_equals?: number;
                in?: string;
                not_in?: string;
                greater_than?: number;
                greater_than_equal?: number;
                less_than?: number;
                less_than_equal?: number;
            };
            height?: {
                equals?: number;
                not_equals?: number;
                in?: string;
                not_in?: string;
                greater_than?: number;
                greater_than_equal?: number;
                less_than?: number;
                less_than_equal?: number;
            };
            focalX?: {
                equals?: number;
                not_equals?: number;
                in?: string;
                not_in?: string;
                greater_than?: number;
                greater_than_equal?: number;
                less_than?: number;
                less_than_equal?: number;
            };
            focalY?: {
                equals?: number;
                not_equals?: number;
                in?: string;
                not_in?: string;
                greater_than?: number;
                greater_than_equal?: number;
                less_than?: number;
                less_than_equal?: number;
            };
        };
        /** Media query conjunction */
        MediaQueryOperationsAnd: {
            and: (components["schemas"]["MediaQueryOperations"] | components["schemas"]["MediaQueryOperationsAnd"] | components["schemas"]["MediaQueryOperationsOr"])[];
        };
        /** Media query disjunction */
        MediaQueryOperationsOr: {
            or: (components["schemas"]["MediaQueryOperations"] | components["schemas"]["MediaQueryOperationsAnd"] | components["schemas"]["MediaQueryOperationsOr"])[];
        };
        /** Page query operations */
        PageQueryOperations: {
            title?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            publishedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            slug?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            slugLock?: {
                equals?: boolean;
                not_equals?: boolean;
                in?: string;
                not_in?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            _status?: {
                /** @enum {string} */
                equals?: "draft" | "published";
                /** @enum {string} */
                not_equals?: "draft" | "published";
                in?: string;
                not_in?: string;
            };
        };
        /** Page query conjunction */
        PageQueryOperationsAnd: {
            and: (components["schemas"]["PageQueryOperations"] | components["schemas"]["PageQueryOperationsAnd"] | components["schemas"]["PageQueryOperationsOr"])[];
        };
        /** Page query disjunction */
        PageQueryOperationsOr: {
            or: (components["schemas"]["PageQueryOperations"] | components["schemas"]["PageQueryOperationsAnd"] | components["schemas"]["PageQueryOperationsOr"])[];
        };
        /** Post query operations */
        PostQueryOperations: {
            title?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            publishedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            slug?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            slugLock?: {
                equals?: boolean;
                not_equals?: boolean;
                in?: string;
                not_in?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            _status?: {
                /** @enum {string} */
                equals?: "draft" | "published";
                /** @enum {string} */
                not_equals?: "draft" | "published";
                in?: string;
                not_in?: string;
            };
        };
        /** Post query conjunction */
        PostQueryOperationsAnd: {
            and: (components["schemas"]["PostQueryOperations"] | components["schemas"]["PostQueryOperationsAnd"] | components["schemas"]["PostQueryOperationsOr"])[];
        };
        /** Post query disjunction */
        PostQueryOperationsOr: {
            or: (components["schemas"]["PostQueryOperations"] | components["schemas"]["PostQueryOperationsAnd"] | components["schemas"]["PostQueryOperationsOr"])[];
        };
        /** Category query operations */
        CategoryQueryOperations: {
            title?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            slug?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            slugLock?: {
                equals?: boolean;
                not_equals?: boolean;
                in?: string;
                not_in?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
        };
        /** Category query conjunction */
        CategoryQueryOperationsAnd: {
            and: (components["schemas"]["CategoryQueryOperations"] | components["schemas"]["CategoryQueryOperationsAnd"] | components["schemas"]["CategoryQueryOperationsOr"])[];
        };
        /** Category query disjunction */
        CategoryQueryOperationsOr: {
            or: (components["schemas"]["CategoryQueryOperations"] | components["schemas"]["CategoryQueryOperationsAnd"] | components["schemas"]["CategoryQueryOperationsOr"])[];
        };
        /** Payload Job query operations */
        PayloadJobQueryOperations: {
            taskSlug?: {
                /** @enum {string} */
                equals?: "inline" | "schedulePublish";
                /** @enum {string} */
                not_equals?: "inline" | "schedulePublish";
                in?: string;
                not_in?: string;
            };
            queue?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            waitUntil?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            processing?: {
                equals?: boolean;
                not_equals?: boolean;
                in?: string;
                not_in?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
        };
        /** Payload Job query conjunction */
        PayloadJobQueryOperationsAnd: {
            and: (components["schemas"]["PayloadJobQueryOperations"] | components["schemas"]["PayloadJobQueryOperationsAnd"] | components["schemas"]["PayloadJobQueryOperationsOr"])[];
        };
        /** Payload Job query disjunction */
        PayloadJobQueryOperationsOr: {
            or: (components["schemas"]["PayloadJobQueryOperations"] | components["schemas"]["PayloadJobQueryOperationsAnd"] | components["schemas"]["PayloadJobQueryOperationsOr"])[];
        };
        /** Payload Locked Document query operations */
        PayloadLockedDocumentQueryOperations: {
            globalSlug?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
        };
        /** Payload Locked Document query conjunction */
        PayloadLockedDocumentQueryOperationsAnd: {
            and: (components["schemas"]["PayloadLockedDocumentQueryOperations"] | components["schemas"]["PayloadLockedDocumentQueryOperationsAnd"] | components["schemas"]["PayloadLockedDocumentQueryOperationsOr"])[];
        };
        /** Payload Locked Document query disjunction */
        PayloadLockedDocumentQueryOperationsOr: {
            or: (components["schemas"]["PayloadLockedDocumentQueryOperations"] | components["schemas"]["PayloadLockedDocumentQueryOperationsAnd"] | components["schemas"]["PayloadLockedDocumentQueryOperationsOr"])[];
        };
        /** Payload Preference query operations */
        PayloadPreferenceQueryOperations: {
            key?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
        };
        /** Payload Preference query conjunction */
        PayloadPreferenceQueryOperationsAnd: {
            and: (components["schemas"]["PayloadPreferenceQueryOperations"] | components["schemas"]["PayloadPreferenceQueryOperationsAnd"] | components["schemas"]["PayloadPreferenceQueryOperationsOr"])[];
        };
        /** Payload Preference query disjunction */
        PayloadPreferenceQueryOperationsOr: {
            or: (components["schemas"]["PayloadPreferenceQueryOperations"] | components["schemas"]["PayloadPreferenceQueryOperationsAnd"] | components["schemas"]["PayloadPreferenceQueryOperationsOr"])[];
        };
        /** Payload Migration query operations */
        PayloadMigrationQueryOperations: {
            name?: {
                equals?: string;
                not_equals?: string;
                in?: string;
                not_in?: string;
                like?: string;
                contains?: string;
            };
            batch?: {
                equals?: number;
                not_equals?: number;
                in?: string;
                not_in?: string;
                greater_than?: number;
                greater_than_equal?: number;
                less_than?: number;
                less_than_equal?: number;
            };
            updatedAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
            createdAt?: {
                /** Format: date-time */
                equals?: string;
                /** Format: date-time */
                not_equals?: string;
                in?: string;
                not_in?: string;
                /** Format: date-time */
                greater_than?: string;
                /** Format: date-time */
                greater_than_equal?: string;
                /** Format: date-time */
                less_than?: string;
                /** Format: date-time */
                less_than_equal?: string;
            };
        };
        /** Payload Migration query conjunction */
        PayloadMigrationQueryOperationsAnd: {
            and: (components["schemas"]["PayloadMigrationQueryOperations"] | components["schemas"]["PayloadMigrationQueryOperationsAnd"] | components["schemas"]["PayloadMigrationQueryOperationsOr"])[];
        };
        /** Payload Migration query disjunction */
        PayloadMigrationQueryOperationsOr: {
            or: (components["schemas"]["PayloadMigrationQueryOperations"] | components["schemas"]["PayloadMigrationQueryOperationsAnd"] | components["schemas"]["PayloadMigrationQueryOperationsOr"])[];
        };
    };
    responses: {
        /** @description User object */
        UserResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["User"];
            };
        };
        /** @description User object */
        NewUserResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["User"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description User not found */
        UserNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Users */
        UserListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["User"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Media object */
        MediaResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["Media"];
            };
        };
        /** @description Media object */
        NewMediaResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["Media"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Media not found */
        MediaNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Media */
        MediaListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["Media"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Page object */
        PageResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["Page"];
            };
        };
        /** @description Page object */
        NewPageResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["Page"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Page not found */
        PageNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Pages */
        PageListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["Page"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Post object */
        PostResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["Post"];
            };
        };
        /** @description Post object */
        NewPostResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["Post"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Post not found */
        PostNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Posts */
        PostListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["Post"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Category object */
        CategoryResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["Category"];
            };
        };
        /** @description Category object */
        NewCategoryResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["Category"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Category not found */
        CategoryNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Categories */
        CategoryListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["Category"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Payload Job object */
        PayloadJobResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["PayloadJob"];
            };
        };
        /** @description Payload Job object */
        NewPayloadJobResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["PayloadJob"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Payload Job not found */
        PayloadJobNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Payload Jobs */
        PayloadJobListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["PayloadJob"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Payload Locked Document object */
        PayloadLockedDocumentResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["PayloadLockedDocument"];
            };
        };
        /** @description Payload Locked Document object */
        NewPayloadLockedDocumentResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["PayloadLockedDocument"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Payload Locked Document not found */
        PayloadLockedDocumentNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Payload Locked Documents */
        PayloadLockedDocumentListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["PayloadLockedDocument"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Payload Preference object */
        PayloadPreferenceResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["PayloadPreference"];
            };
        };
        /** @description Payload Preference object */
        NewPayloadPreferenceResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["PayloadPreference"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Payload Preference not found */
        PayloadPreferenceNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Payload Preferences */
        PayloadPreferenceListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["PayloadPreference"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
        /** @description Payload Migration object */
        PayloadMigrationResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": components["schemas"]["PayloadMigration"];
            };
        };
        /** @description Payload Migration object */
        NewPayloadMigrationResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    message: string;
                    doc: components["schemas"]["PayloadMigration"] & {
                        id: string;
                        /** Format: date-time */
                        createdAt: string;
                        /** Format: date-time */
                        updatedAt: string;
                    };
                };
            };
        };
        /** @description Payload Migration not found */
        PayloadMigrationNotFoundResponse: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description List of Payload Migrations */
        PayloadMigrationListResponse: {
            headers: {
                [name: string]: unknown;
            };
            content: {
                "application/json": {
                    docs: components["schemas"]["PayloadMigration"][];
                    totalDocs: number;
                    limit: number;
                    totalPages: number;
                    page: number;
                    pagingCounter: number;
                    hasPrevPage: boolean;
                    hasNextPage: boolean;
                    prevPage: number | null;
                    nextPage: number | null;
                };
            };
        };
    };
    parameters: never;
    requestBodies: {
        /** @description User */
        UserRequestBody: {
            content: {
                "application/json": {
                    name?: string | null;
                    enableAPIKey?: boolean | null;
                    apiKey?: string | null;
                    apiKeyIndex?: string | null;
                    email: string;
                    resetPasswordToken?: string | null;
                    resetPasswordExpiration?: string | null;
                    salt?: string | null;
                    hash?: string | null;
                    loginAttempts?: number | null;
                    lockUntil?: string | null;
                    sessions?: {
                        id: string;
                        createdAt?: string | null;
                        expiresAt: string;
                    }[] | null;
                    password?: string | null;
                };
            };
        };
        /** @description Media */
        MediaRequestBody: {
            content: {
                "application/json": {
                    alt?: string | null;
                    caption?: {
                        root: {
                            type: string;
                            children: ({
                                type: string;
                                version: number;
                            } & {
                                [key: string]: unknown;
                            })[];
                            direction: "ltr" | "rtl" | null;
                            /** @enum {string} */
                            format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                            indent: number;
                            version: number;
                        };
                    } | null;
                    url?: string | null;
                    thumbnailURL?: string | null;
                    filename?: string | null;
                    mimeType?: string | null;
                    filesize?: number | null;
                    width?: number | null;
                    height?: number | null;
                    focalX?: number | null;
                    focalY?: number | null;
                    sizes?: {
                        thumbnail?: {
                            url?: string | null;
                            width?: number | null;
                            height?: number | null;
                            mimeType?: string | null;
                            filesize?: number | null;
                            filename?: string | null;
                        };
                        square?: {
                            url?: string | null;
                            width?: number | null;
                            height?: number | null;
                            mimeType?: string | null;
                            filesize?: number | null;
                            filename?: string | null;
                        };
                        small?: {
                            url?: string | null;
                            width?: number | null;
                            height?: number | null;
                            mimeType?: string | null;
                            filesize?: number | null;
                            filename?: string | null;
                        };
                        medium?: {
                            url?: string | null;
                            width?: number | null;
                            height?: number | null;
                            mimeType?: string | null;
                            filesize?: number | null;
                            filename?: string | null;
                        };
                        large?: {
                            url?: string | null;
                            width?: number | null;
                            height?: number | null;
                            mimeType?: string | null;
                            filesize?: number | null;
                            filename?: string | null;
                        };
                        xlarge?: {
                            url?: string | null;
                            width?: number | null;
                            height?: number | null;
                            mimeType?: string | null;
                            filesize?: number | null;
                            filename?: string | null;
                        };
                        og?: {
                            url?: string | null;
                            width?: number | null;
                            height?: number | null;
                            mimeType?: string | null;
                            filesize?: number | null;
                            filename?: string | null;
                        };
                    };
                };
            };
        };
        /** @description Page */
        PageRequestBody: {
            content: {
                "application/json": {
                    title: string;
                    layout: ({
                        richText?: {
                            root: {
                                type: string;
                                children: ({
                                    type: string;
                                    version: number;
                                } & {
                                    [key: string]: unknown;
                                })[];
                                direction: "ltr" | "rtl" | null;
                                /** @enum {string} */
                                format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                                indent: number;
                                version: number;
                            };
                        } | null;
                        links?: {
                            link: {
                                /** @enum {string|null} */
                                type?: "reference" | "custom" | null;
                                newTab?: boolean | null;
                                reference?: ({
                                    /** @enum {unknown} */
                                    relationTo: "pages";
                                    value: string | components["schemas"]["Page"];
                                } | null) | ({
                                    /** @enum {unknown} */
                                    relationTo: "posts";
                                    value: string | components["schemas"]["Post"];
                                } | null);
                                url?: string | null;
                                label: string;
                                /**
                                 * @description Choose how the link should be rendered.
                                 * @enum {string|null}
                                 */
                                appearance?: "default" | "outline" | null;
                            };
                            id?: string | null;
                        }[] | null;
                        id?: string | null;
                        blockName?: string | null;
                        /** @enum {unknown} */
                        blockType: "cta";
                    } | {
                        columns?: {
                            /** @enum {string|null} */
                            size?: "oneThird" | "half" | "twoThirds" | "full" | null;
                            richText?: {
                                root: {
                                    type: string;
                                    children: ({
                                        type: string;
                                        version: number;
                                    } & {
                                        [key: string]: unknown;
                                    })[];
                                    direction: "ltr" | "rtl" | null;
                                    /** @enum {string} */
                                    format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                                    indent: number;
                                    version: number;
                                };
                            } | null;
                            enableLink?: boolean | null;
                            link?: {
                                /** @enum {string|null} */
                                type?: "reference" | "custom" | null;
                                newTab?: boolean | null;
                                reference?: ({
                                    /** @enum {unknown} */
                                    relationTo: "pages";
                                    value: string | components["schemas"]["Page"];
                                } | null) | ({
                                    /** @enum {unknown} */
                                    relationTo: "posts";
                                    value: string | components["schemas"]["Post"];
                                } | null);
                                url?: string | null;
                                label: string;
                                /**
                                 * @description Choose how the link should be rendered.
                                 * @enum {string|null}
                                 */
                                appearance?: "default" | "outline" | null;
                            };
                            id?: string | null;
                        }[] | null;
                        id?: string | null;
                        blockName?: string | null;
                        /** @enum {unknown} */
                        blockType: "content";
                    } | {
                        media: string | components["schemas"]["Media"];
                        id?: string | null;
                        blockName?: string | null;
                        /** @enum {unknown} */
                        blockType: "mediaBlock";
                    } | {
                        introContent?: {
                            root: {
                                type: string;
                                children: ({
                                    type: string;
                                    version: number;
                                } & {
                                    [key: string]: unknown;
                                })[];
                                direction: "ltr" | "rtl" | null;
                                /** @enum {string} */
                                format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                                indent: number;
                                version: number;
                            };
                        } | null;
                        /** @enum {string|null} */
                        populateBy?: "collection" | "selection" | null;
                        /** @enum {string|null} */
                        relationTo?: "posts" | null;
                        categories?: (string | components["schemas"]["Category"])[] | null;
                        limit?: number | null;
                        selectedDocs?: {
                            /** @enum {unknown} */
                            relationTo: "posts";
                            value: string | components["schemas"]["Post"];
                        }[] | null;
                        id?: string | null;
                        blockName?: string | null;
                        /** @enum {unknown} */
                        blockType: "archive";
                    })[];
                    meta?: {
                        title?: string | null;
                        /** @description Maximum upload file size: 12MB. Recommended file size for images is <500KB. */
                        image?: (string | null) | components["schemas"]["Media"];
                        description?: string | null;
                    };
                    publishedAt?: string | null;
                    slug?: string | null;
                    slugLock?: boolean | null;
                    /** @enum {string|null} */
                    _status?: "draft" | "published" | null;
                };
            };
        };
        /** @description Post */
        PostRequestBody: {
            content: {
                "application/json": {
                    title: string;
                    heroImage?: (string | null) | components["schemas"]["Media"];
                    content: {
                        root: {
                            type: string;
                            children: ({
                                type: string;
                                version: number;
                            } & {
                                [key: string]: unknown;
                            })[];
                            direction: "ltr" | "rtl" | null;
                            /** @enum {string} */
                            format: "left" | "start" | "center" | "right" | "end" | "justify" | "";
                            indent: number;
                            version: number;
                        };
                    };
                    relatedPosts?: (string | components["schemas"]["Post"])[] | null;
                    categories?: (string | components["schemas"]["Category"])[] | null;
                    meta?: {
                        title?: string | null;
                        /** @description Maximum upload file size: 12MB. Recommended file size for images is <500KB. */
                        image?: (string | null) | components["schemas"]["Media"];
                        description?: string | null;
                    };
                    publishedAt?: string | null;
                    /** @description ID of the users */
                    authors?: string;
                    populatedAuthors?: {
                        id?: string | null;
                        name?: string | null;
                    }[] | null;
                    slug?: string | null;
                    slugLock?: boolean | null;
                    /** @enum {string|null} */
                    _status?: "draft" | "published" | null;
                };
            };
        };
        /** @description Category */
        CategoryRequestBody: {
            content: {
                "application/json": {
                    title: string;
                    slug?: string | null;
                    slugLock?: boolean | null;
                };
            };
        };
        /** @description Payload Job */
        PayloadJobRequestBody: {
            content: {
                "application/json": {
                    /** @description Input data provided to the job */
                    input?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                    taskStatus?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                    completedAt?: string | null;
                    totalTried?: number | null;
                    /** @description If hasError is true this job will not be retried */
                    hasError?: boolean | null;
                    /** @description If hasError is true, this is the error that caused it */
                    error?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                    /** @description Task execution log */
                    log?: {
                        executedAt: string;
                        completedAt: string;
                        /** @enum {string} */
                        taskSlug: "inline" | "schedulePublish";
                        taskID: string;
                        input?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                        output?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                        /** @enum {string} */
                        state: "failed" | "succeeded";
                        error?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                        id?: string | null;
                    }[] | null;
                    /** @enum {string|null} */
                    taskSlug?: "inline" | "schedulePublish" | null;
                    queue?: string | null;
                    waitUntil?: string | null;
                    processing?: boolean | null;
                };
            };
        };
        /** @description Payload Locked Document */
        PayloadLockedDocumentRequestBody: {
            content: {
                "application/json": {
                    /** @description ID of the users/media/pages/posts/categories/payload-jobs */
                    document?: string;
                    globalSlug?: string | null;
                    /** @description ID of the users */
                    user: string;
                };
            };
        };
        /** @description Payload Preference */
        PayloadPreferenceRequestBody: {
            content: {
                "application/json": {
                    /** @description ID of the users */
                    user: string;
                    key?: string | null;
                    value?: (Record<string, never> | unknown[] | string | number | boolean) | null;
                };
            };
        };
        /** @description Payload Migration */
        PayloadMigrationRequestBody: {
            content: {
                "application/json": {
                    name?: string | null;
                    batch?: number | null;
                };
            };
        };
    };
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;

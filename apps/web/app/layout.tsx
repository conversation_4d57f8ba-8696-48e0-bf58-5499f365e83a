import '../styles/globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { CartProvider } from '@/components/cart/CartProvider';
import { CartDrawer } from '@/components/cart/CartDrawer';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter'
});

export const metadata: Metadata = {
  title: 'Autopiloot - Automatiseeri oma äri 24/7 | N8N mallid Eesti ettevõtjatele',
  description: 'Pane oma äri autopiloodi! N8N töövoo mallid ja starterpaketid vabakutselistele ja väikettevõtjatele. Automatiseeri oma igapäevased ülesanded juba täna.',
  keywords: 'automaatika, n8n, töövoo mallid, äriprotsessid, automatiseerimine, E<PERSON>i, vabakutselised',
  openGraph: {
    title: 'Autopiloot - Automatiseeri oma äri',
    description: 'N8N töövoo mallid Eesti ettevõtjatele. Rohkem aega, vähem rutiini.',
    type: 'website',
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const messages = await getMessages();

  return (
    <html lang="et" className="dark">
      <body className={`${inter.variable} font-sans antialiased`}>
        <NextIntlClientProvider messages={messages}>
          <CartProvider>
            {children}
            <CartDrawer />
          </CartProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download,
  Eye,
  Star,
  Clock,
  Calendar,
  Search,
  Filter,
  User,
  Settings,
  CreditCard,
  FileText,
  Play,
  BookOpen,
  ExternalLink,
  CheckCircle2,
  AlertCircle,
  Mail,
  Database,
  ShoppingCart,
  MessageSquare,
  Zap
} from 'lucide-react';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import Link from 'next/link';

// Mock user data
const userData = {
  name: "Mark<PERSON>",
  email: "<EMAIL>",
  joinDate: "2024-01-15",
  totalPurchases: 5,
  totalSaved: 89,
  avatar: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?w=150&h=150&fit=crop&crop=face"
};

// Mock purchased templates
const purchasedTemplates = [
  {
    id: 1,
    title: "E-mail Marketing Automaatika",
    description: "Täielik e-mail marketing süsteem automaatsete kampaaniate ja järelkontrollidega",
    purchaseDate: "2024-03-15",
    price: "€29",
    status: "active",
    downloads: 3,
    lastDownload: "2024-03-20",
    icon: Mail,
    color: "cyan",
    difficulty: "Lihtne",
    setupTime: "5 min",
    version: "2.1",
    category: "Marketing",
    files: [
      { name: "email-marketing-workflow.json", size: "45 KB", type: "N8N Workflow" },
      { name: "setup-guide-et.pdf", size: "2.1 MB", type: "Juhend" },
      { name: "video-tutorial.mp4", size: "125 MB", type: "Video õpetus" }
    ]
  },
  {
    id: 2,
    title: "CRM Täielik Integratsioon",
    description: "Klientide andmete sünkroniseerimine ja automaatne staatuste uuendamine",
    purchaseDate: "2024-03-10",
    price: "€39",
    status: "active",
    downloads: 5,
    lastDownload: "2024-03-18",
    icon: Database,
    color: "green",
    difficulty: "Keskmine",
    setupTime: "10 min",
    version: "1.8",
    category: "CRM",
    files: [
      { name: "crm-integration.json", size: "67 KB", type: "N8N Workflow" },
      { name: "database-schema.sql", size: "12 KB", type: "Andmebaas" },
      { name: "setup-guide-et.pdf", size: "3.2 MB", type: "Juhend" }
    ]
  },
  {
    id: 3,
    title: "E-kaubanduse Automaatika",
    description: "Tellimuste töötlemine, varude jälgimine ja klientide teavitamine",
    purchaseDate: "2024-02-28",
    price: "€45",
    status: "active",
    downloads: 2,
    lastDownload: "2024-03-01",
    icon: ShoppingCart,
    color: "orange",
    difficulty: "Keeruline",
    setupTime: "15 min",
    version: "3.0",
    category: "E-kaubandus",
    files: [
      { name: "ecommerce-automation.json", size: "89 KB", type: "N8N Workflow" },
      { name: "webhook-endpoints.json", size: "8 KB", type: "Konfiguratsioon" },
      { name: "setup-guide-et.pdf", size: "4.1 MB", type: "Juhend" },
      { name: "advanced-tutorial.mp4", size: "245 MB", type: "Video õpetus" }
    ]
  }
];

const recentActivity = [
  { type: "download", template: "E-mail Marketing Automaatika", date: "2024-03-20", icon: Download },
  { type: "purchase", template: "CRM Täielik Integratsioon", date: "2024-03-10", icon: ShoppingCart },
  { type: "view", template: "E-kaubanduse Automaatika", date: "2024-03-08", icon: Eye },
  { type: "download", template: "CRM Täielik Integratsioon", date: "2024-03-05", icon: Download }
];

const colorClasses = {
  cyan: { bg: "bg-cyan-500/10", border: "border-cyan-500/30", text: "text-cyan-300", icon: "text-cyan-400" },
  green: { bg: "bg-green-500/10", border: "border-green-500/30", text: "text-green-300", icon: "text-green-400" },
  orange: { bg: "bg-orange-500/10", border: "border-orange-500/30", text: "text-orange-300", icon: "text-orange-400" },
  purple: { bg: "bg-purple-500/10", border: "border-purple-500/30", text: "text-purple-300", icon: "text-purple-400" }
};

export default function DashboardPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<typeof purchasedTemplates[0] | null>(null);

  const filteredTemplates = purchasedTemplates.filter(template =>
    template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
        {/* Background Effects */}
        <div className="fixed inset-0 z-0">
          <ParticleSystem />
          <AnimatedPipes />
        </div>

        {/* Content */}
        <div className="relative z-10 pt-20">
          <div className="container mx-auto px-4 py-12">
            {/* Welcome Header */}
            <div className="mb-12">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                <div className="flex items-center gap-4">
                  <img 
                    src={userData.avatar} 
                    alt={userData.name}
                    className="w-16 h-16 rounded-full border-4 border-cyan-500/30"
                  />
                  <div>
                    <h1 className="text-3xl font-bold text-white">
                      Tere tulemast, {userData.name}! 👋
                    </h1>
                    <p className="text-slate-300">
                      Sinu automatiseerimise keskus - {userData.totalPurchases} malli ostetud
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <Button 
                    variant="outline"
                    className="border-slate-600 text-slate-300 hover:bg-slate-700/50"
                    asChild
                  >
                    <Link href="/store">
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      Vaata rohkem malle
                    </Link>
                  </Button>
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-cyan-500/10 border border-cyan-500/20 rounded-lg">
                    <FileText className="w-6 h-6 text-cyan-400" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">{userData.totalPurchases}</div>
                    <div className="text-sm text-slate-400">Ostetud malli</div>
                  </div>
                </div>
              </Card>
              
              <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                    <Download className="w-6 h-6 text-green-400" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {purchasedTemplates.reduce((sum, t) => sum + t.downloads, 0)}
                    </div>
                    <div className="text-sm text-slate-400">Allalaadimist</div>
                  </div>
                </div>
              </Card>
              
              <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                    <Clock className="w-6 h-6 text-purple-400" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">{userData.totalSaved}h</div>
                    <div className="text-sm text-slate-400">Aega kokku hoitud</div>
                  </div>
                </div>
              </Card>
              
              <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                    <Star className="w-6 h-6 text-orange-400" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">Pro</div>
                    <div className="text-sm text-slate-400">Kasutaja staatus</div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Main Content Tabs */}
            <Tabs defaultValue="templates" className="space-y-8">
              <TabsList className="bg-slate-800/50 border border-slate-700/50">
                <TabsTrigger value="templates" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-300">
                  <FileText className="w-4 h-4 mr-2" />
                  Minu mallid
                </TabsTrigger>
                <TabsTrigger value="activity" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-300">
                  <Clock className="w-4 h-4 mr-2" />
                  Tegevused
                </TabsTrigger>
                <TabsTrigger value="settings" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-300">
                  <Settings className="w-4 h-4 mr-2" />
                  Seaded
                </TabsTrigger>
              </TabsList>

              {/* Templates Tab */}
              <TabsContent value="templates" className="space-y-6">
                {/* Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                    <Input
                      type="text"
                      placeholder="Otsi malle..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400"
                    />
                  </div>
                  <Button 
                    variant="outline"
                    className="border-slate-600 text-slate-300 hover:bg-slate-700/50"
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    Filter
                  </Button>
                </div>

                {/* Templates Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredTemplates.map((template) => {
                    const IconComponent = template.icon;
                    const colorClass = colorClasses[template.color as keyof typeof colorClasses];
                    
                    return (
                      <Card 
                        key={template.id}
                        className="bg-slate-800/50 border-slate-700/50 p-6 hover:bg-slate-800/80 transition-all duration-300 group"
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className={`p-3 rounded-lg ${colorClass.bg} ${colorClass.border} border`}>
                            <IconComponent className={`w-6 h-6 ${colorClass.icon}`} />
                          </div>
                          <Badge 
                            variant="outline" 
                            className={`${colorClass.border} ${colorClass.text} border`}
                          >
                            v{template.version}
                          </Badge>
                        </div>

                        <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                          {template.title}
                        </h3>
                        <p className="text-slate-300 text-sm mb-4 leading-relaxed">
                          {template.description}
                        </p>

                        <div className="flex items-center gap-4 mb-4 text-sm text-slate-400">
                          <div className="flex items-center gap-1">
                            <Download className="w-4 h-4" />
                            <span>{template.downloads}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{template.setupTime}</span>
                          </div>
                          <Badge variant="outline" className="border-slate-600 text-slate-300 text-xs">
                            {template.difficulty}
                          </Badge>
                        </div>

                        <div className="flex gap-2">
                          <Button 
                            size="sm"
                            className={`flex-1 bg-gradient-to-r from-${template.color}-500 to-${template.color}-600 hover:opacity-90 text-white`}
                            onClick={() => setSelectedTemplate(template)}
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            Vaata
                          </Button>
                          <Button 
                            size="sm"
                            variant="outline"
                            className="border-slate-600 text-slate-300 hover:bg-slate-700/50"
                            asChild
                          >
                            <Link href={`/template/${template.id}`}>
                              <ExternalLink className="w-4 h-4" />
                            </Link>
                          </Button>
                        </div>
                      </Card>
                    );
                  })}
                </div>
              </TabsContent>

              {/* Activity Tab */}
              <TabsContent value="activity" className="space-y-6">
                <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                  <h3 className="text-xl font-bold text-white mb-6">Viimased tegevused</h3>
                  <div className="space-y-4">
                    {recentActivity.map((activity, index) => {
                      const IconComponent = activity.icon;
                      return (
                        <div key={index} className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-lg">
                          <div className="p-2 bg-slate-600/50 rounded-lg">
                            <IconComponent className="w-5 h-5 text-slate-300" />
                          </div>
                          <div className="flex-1">
                            <p className="text-white font-medium">
                              {activity.type === 'download' && 'Laadisid alla: '}
                              {activity.type === 'purchase' && 'Ostsid: '}
                              {activity.type === 'view' && 'Vaatasid: '}
                              {activity.template}
                            </p>
                            <p className="text-slate-400 text-sm">{activity.date}</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </Card>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                    <h3 className="text-xl font-bold text-white mb-6">Profiili andmed</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">Nimi</label>
                        <Input 
                          defaultValue={userData.name}
                          className="bg-slate-700/50 border-slate-600/50 text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">E-mail</label>
                        <Input 
                          defaultValue={userData.email}
                          className="bg-slate-700/50 border-slate-600/50 text-white"
                        />
                      </div>
                      <Button className="bg-cyan-500 hover:bg-cyan-400 text-white">
                        Salvesta muudatused
                      </Button>
                    </div>
                  </Card>

                  <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                    <h3 className="text-xl font-bold text-white mb-6">Teavitused</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-slate-300">Uued mallid</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-300">Uuendused</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-300">Turundus</span>
                        <input type="checkbox" className="rounded" />
                      </div>
                    </div>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Template Detail Modal */}
        {selectedTemplate && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <Card className="bg-slate-800 border-slate-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-8">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className={`p-4 rounded-lg ${colorClasses[selectedTemplate.color as keyof typeof colorClasses].bg} ${colorClasses[selectedTemplate.color as keyof typeof colorClasses].border} border`}>
                      <selectedTemplate.icon className={`w-8 h-8 ${colorClasses[selectedTemplate.color as keyof typeof colorClasses].icon}`} />
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-2">{selectedTemplate.title}</h2>
                      <p className="text-slate-300">{selectedTemplate.description}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedTemplate(null)}
                    className="text-slate-400 hover:text-white transition-colors"
                  >
                    ✕
                  </button>
                </div>

                <div className="grid lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-bold text-white mb-4">Failid</h3>
                    <div className="space-y-3">
                      {selectedTemplate.files.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                          <div>
                            <p className="text-white font-medium">{file.name}</p>
                            <p className="text-slate-400 text-sm">{file.type} • {file.size}</p>
                          </div>
                          <Button size="sm" className="bg-cyan-500 hover:bg-cyan-400 text-white">
                            <Download className="w-4 h-4 mr-2" />
                            Laadi alla
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-bold text-white mb-4">Detailid</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Ostetud:</span>
                        <span className="text-white">{selectedTemplate.purchaseDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Versioon:</span>
                        <span className="text-white">v{selectedTemplate.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Allalaadimisi:</span>
                        <span className="text-white">{selectedTemplate.downloads}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Viimane allalaadimine:</span>
                        <span className="text-white">{selectedTemplate.lastDownload}</span>
                      </div>
                    </div>

                    <div className="mt-6 space-y-3">
                      <Button 
                        className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:opacity-90 text-white"
                        asChild
                      >
                        <Link href={`/template/${selectedTemplate.id}`}>
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Ava detailvaade
                        </Link>
                      </Button>
                      <Button 
                        variant="outline"
                        className="w-full border-slate-600 text-slate-300 hover:bg-slate-700/50"
                      >
                        <BookOpen className="w-4 h-4 mr-2" />
                        Vaata juhendit
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
    </main>
  );
}
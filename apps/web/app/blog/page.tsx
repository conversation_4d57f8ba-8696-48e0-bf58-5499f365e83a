'use client';

import { Metada<PERSON> } from 'next';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';

export default function BlogPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
      {/* Background Effects */}
      <div className="fixed inset-0 z-0">
        <ParticleSystem />
        <AnimatedPipes />
      </div>

      {/* Content */}
      <div className="relative z-10 pt-20">
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Blogi
            </h1>
            <p className="text-xl text-slate-300 mb-12">
              Loe meie artikleid automatiseerimise ja äriprotsesside kohta.
            </p>

            <div className="text-center py-16">
              <p className="text-slate-400 text-lg">
                Blogi sisu tuleb peagi...
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

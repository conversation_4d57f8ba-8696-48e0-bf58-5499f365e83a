'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard,
  Lock,
  Shield,
  CheckCircle2,
  ArrowLeft,
  Mail,
  User,
  MapPin,
  Phone,
  Building,
  Globe,
  Zap,
  Download,
  Clock
} from 'lucide-react';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import { useCartHelpers } from '@/components/cart/CartProvider';
import Link from 'next/link';

const paymentMethods = [
  { id: 'card', name: 'Krediitkaart', icon: CreditCard, description: 'Visa, Mastercard, American Express' },
  { id: 'paypal', name: 'PayPal', icon: Globe, description: 'Turvaline PayPal maksmine' },
  { id: 'bank', name: 'Pangaülekanne', icon: Building, description: 'Eesti pangad (Swedbank, SEB, LHV)' }
];

export default function CheckoutPage() {
  const { items, getTotalPrice, getSavings, clearCart } = useCartHelpers();
  const [selectedPayment, setSelectedPayment] = useState('card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderComplete, setOrderComplete] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    setIsProcessing(false);
    setOrderComplete(true);
    clearCart();
  };

  if (orderComplete) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
          <div className="fixed inset-0 z-0">
            <ParticleSystem />
            <AnimatedPipes />
          </div>

          <div className="relative z-10 pt-20">
            <div className="container mx-auto px-4 py-20">
              <div className="max-w-2xl mx-auto text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-8">
                  <CheckCircle2 className="w-10 h-10 text-white" />
                </div>
                
                <h1 className="text-4xl font-bold text-white mb-6">
                  Tellimus edukalt loodud! 🎉
                </h1>
                
                <p className="text-xl text-slate-300 mb-8">
                  Sinu N8N mallid on valmis allalaadimiseks. Kontrollimise e-maili saatsime sulle kinnituse koos allalaadimise linkidega.
                </p>

                <Card className="bg-slate-800/50 border-slate-700/50 p-8 mb-8">
                  <h3 className="text-xl font-bold text-white mb-4">Järgmised sammud:</h3>
                  <div className="space-y-4 text-left">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-cyan-500/20 border border-cyan-500/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-cyan-400 text-sm font-bold">1</span>
                      </div>
                      <div>
                        <p className="text-white font-medium">Kontrolli oma e-maili</p>
                        <p className="text-slate-400 text-sm">Saatsime sulle allalaadimise lingid ja seadistamise juhendid</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-green-500/20 border border-green-500/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-green-400 text-sm font-bold">2</span>
                      </div>
                      <div>
                        <p className="text-white font-medium">Laadi alla mallid</p>
                        <p className="text-slate-400 text-sm">Kõik ostetud mallid on kohe allalaadimiseks valmis</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-purple-500/20 border border-purple-500/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-purple-400 text-sm font-bold">3</span>
                      </div>
                      <div>
                        <p className="text-white font-medium">Seadista N8N-is</p>
                        <p className="text-slate-400 text-sm">Järgi meie detailseid juhendeid ja video õpetusi</p>
                      </div>
                    </div>
                  </div>
                </Card>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button 
                    className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-8 py-3"
                    asChild
                  >
                    <Link href="/store">
                      Vaata rohkem malle
                    </Link>
                  </Button>
                  <Button 
                    variant="outline"
                    className="border-slate-600 text-slate-300 hover:bg-slate-700/50 px-8 py-3"
                    asChild
                  >
                    <Link href="/">
                      Tagasi avalehele
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
      </main>
    );
  }

  if (items.length === 0) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
          <div className="fixed inset-0 z-0">
            <ParticleSystem />
            <AnimatedPipes />
          </div>

          <div className="relative z-10 pt-20">
            <div className="container mx-auto px-4 py-20">
              <div className="max-w-2xl mx-auto text-center">
                <h1 className="text-4xl font-bold text-white mb-6">
                  Ostukorv on tühi
                </h1>
                <p className="text-xl text-slate-300 mb-8">
                  Lisa malle ostukorvi, et jätkata ostuga
                </p>
                <Button 
                  className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-8 py-3"
                  asChild
                >
                  <Link href="/store">
                    Vaata malle
                  </Link>
                </Button>
              </div>
            </div>
          </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
        {/* Background Effects */}
        <div className="fixed inset-0 z-0">
          <ParticleSystem />
          <AnimatedPipes />
        </div>

        {/* Content */}
        <div className="relative z-10 pt-20">
          <div className="container mx-auto px-4 py-12">
            {/* Header */}
            <div className="flex items-center gap-4 mb-8">
              <Button 
                variant="outline" 
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700/50"
                asChild
              >
                <Link href="/store">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Tagasi poodi
                </Link>
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-white">Kassa</h1>
                <p className="text-slate-400">Lõpeta oma tellimus ja alusta automatiseerimist</p>
              </div>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* Checkout Form */}
              <div className="space-y-8">
                {/* Contact Information */}
                <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                  <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                    <User className="w-5 h-5 text-cyan-400" />
                    Kontaktandmed
                  </h2>
                  
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName" className="text-slate-300">Eesnimi</Label>
                        <Input 
                          id="firstName" 
                          required
                          className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                          placeholder="Sinu eesnimi"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName" className="text-slate-300">Perekonnanimi</Label>
                        <Input 
                          id="lastName" 
                          required
                          className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                          placeholder="Sinu perekonnanimi"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="email" className="text-slate-300">E-mail</Label>
                      <Input 
                        id="email" 
                        type="email" 
                        required
                        className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="phone" className="text-slate-300">Telefon (valikuline)</Label>
                      <Input 
                        id="phone" 
                        type="tel"
                        className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                        placeholder="+372 5555 5555"
                      />
                    </div>

                    <div>
                      <Label htmlFor="company" className="text-slate-300">Ettevõte (valikuline)</Label>
                      <Input 
                        id="company"
                        className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                        placeholder="Sinu ettevõtte nimi"
                      />
                    </div>
                  </form>
                </Card>

                {/* Payment Method */}
                <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                  <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                    <CreditCard className="w-5 h-5 text-green-400" />
                    Makseviis
                  </h2>
                  
                  <div className="space-y-3 mb-6">
                    {paymentMethods.map((method) => {
                      const IconComponent = method.icon;
                      return (
                        <label 
                          key={method.id}
                          className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer transition-all ${
                            selectedPayment === method.id
                              ? 'border-cyan-500/50 bg-cyan-500/10'
                              : 'border-slate-600/50 bg-slate-700/30 hover:bg-slate-700/50'
                          }`}
                        >
                          <input
                            type="radio"
                            name="payment"
                            value={method.id}
                            checked={selectedPayment === method.id}
                            onChange={(e) => setSelectedPayment(e.target.value)}
                            className="sr-only"
                          />
                          <IconComponent className={`w-5 h-5 ${selectedPayment === method.id ? 'text-cyan-400' : 'text-slate-400'}`} />
                          <div className="flex-1">
                            <div className={`font-medium ${selectedPayment === method.id ? 'text-white' : 'text-slate-300'}`}>
                              {method.name}
                            </div>
                            <div className="text-sm text-slate-400">{method.description}</div>
                          </div>
                          <div className={`w-4 h-4 rounded-full border-2 ${
                            selectedPayment === method.id 
                              ? 'border-cyan-400 bg-cyan-400' 
                              : 'border-slate-500'
                          }`}>
                            {selectedPayment === method.id && (
                              <div className="w-full h-full rounded-full bg-white scale-50"></div>
                            )}
                          </div>
                        </label>
                      );
                    })}
                  </div>

                  {selectedPayment === 'card' && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="cardNumber" className="text-slate-300">Kaardi number</Label>
                        <Input 
                          id="cardNumber" 
                          placeholder="1234 5678 9012 3456"
                          className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="expiry" className="text-slate-300">Kehtivus</Label>
                          <Input 
                            id="expiry" 
                            placeholder="MM/YY"
                            className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                          />
                        </div>
                        <div>
                          <Label htmlFor="cvc" className="text-slate-300">CVC</Label>
                          <Input 
                            id="cvc" 
                            placeholder="123"
                            className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </Card>

                {/* Security Notice */}
                <div className="flex items-center gap-3 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <Shield className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="text-green-300 font-medium">Turvaline maksmine</p>
                    <p className="text-slate-400">Sinu andmed on krüpteeritud ja turvaliselt kaitstud</p>
                  </div>
                </div>
              </div>

              {/* Order Summary */}
              <div className="space-y-6">
                <Card className="bg-slate-800/50 border-slate-700/50 p-6 sticky top-24">
                  <h2 className="text-xl font-bold text-white mb-6">Tellimuse kokkuvõte</h2>
                  
                  <div className="space-y-4 mb-6">
                    {items.map((item) => {
                      const IconComponent = item.icon;
                      return (
                        <div key={item.id} className="flex items-start gap-4 p-4 bg-slate-700/30 rounded-lg">
                          <div className="p-2 bg-cyan-500/10 border border-cyan-500/20 rounded-lg">
                            <IconComponent className="w-5 h-5 text-cyan-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-white text-sm mb-1">{item.title}</h3>
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline" className="border-slate-600 text-slate-300 text-xs">
                                {item.difficulty}
                              </Badge>
                              <div className="flex items-center gap-1 text-xs text-slate-400">
                                <Clock className="w-3 h-3" />
                                {item.setupTime}
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <span className="text-white font-bold">{item.price}</span>
                                <span className="text-slate-400 line-through text-sm">{item.originalPrice}</span>
                              </div>
                              <span className="text-slate-400 text-sm">× {item.quantity}</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  <Separator className="bg-slate-700/50 my-6" />

                  <div className="space-y-3">
                    <div className="flex justify-between text-slate-300">
                      <span>Vahesumma:</span>
                      <span>€{getTotalPrice().toFixed(2)}</span>
                    </div>
                    {getSavings() > 0 && (
                      <div className="flex justify-between text-green-400">
                        <span>Kokkuhoid:</span>
                        <span>-€{getSavings().toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-slate-300">
                      <span>KM (20%):</span>
                      <span>€{(getTotalPrice() * 0.2).toFixed(2)}</span>
                    </div>
                    <Separator className="bg-slate-700/50" />
                    <div className="flex justify-between text-xl font-bold text-white">
                      <span>Kokku:</span>
                      <span>€{(getTotalPrice() * 1.2).toFixed(2)}</span>
                    </div>
                  </div>

                  <Button 
                    onClick={handleSubmit}
                    disabled={isProcessing}
                    className="w-full mt-6 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 text-white py-4 text-lg font-semibold transition-all duration-300 group"
                  >
                    {isProcessing ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                        Töötleme makset...
                      </>
                    ) : (
                      <>
                        <Lock className="w-5 h-5 mr-2" />
                        Lõpeta tellimus €{(getTotalPrice() * 1.2).toFixed(2)}
                      </>
                    )}
                  </Button>

                  <div className="mt-4 space-y-2 text-xs text-slate-400">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="w-4 h-4 text-green-400" />
                      <span>30-päevane raha tagasi garantii</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Download className="w-4 h-4 text-cyan-400" />
                      <span>Kohene allalaadimine pärast makset</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Zap className="w-4 h-4 text-purple-400" />
                      <span>Tasuta uuendused ja tugi</span>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
    </main>
  );
}
'use client';

import { Head<PERSON> } from '@/components/layout/Header';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Target, 
  Zap, 
  Heart, 
  Award, 
  Clock,
  ArrowRight,
  CheckCircle2,
  Lightbulb,
  Globe,
  Mail,
  Linkedin
} from 'lucide-react';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import { AnimatedCounter } from '@/components/animations/AnimatedCounter';

const values = [
  {
    icon: Target,
    title: "Lihtne ja tõhus",
    description: "Loome lahendusi, mis on lihtsad kasutada, kuid võimsad tulemuste poolest",
    color: "cyan"
  },
  {
    icon: Heart,
    title: "Kliendikeskne",
    description: "Iga lahendus on loodud mõeldes teie aja ja energia säästmisele",
    color: "green"
  },
  {
    icon: Zap,
    title: "Innovaatiline",
    description: "Kasutame uusimaid tehnoloogiaid, et pakkuda parimaid automatiseerimise lahendusi",
    color: "purple"
  },
  {
    icon: Globe,
    title: "Eesti kvaliteet",
    description: "Uhked oma Eesti päritolu üle ja pakume kohalikku tuge eesti keeles",
    color: "orange"
  }
];

const team = [
  {
    name: "Marko Tamm",
    role: "Asutaja & CEO",
    description: "10+ aastat kogemust äriprotsesside automatiseerimisel",
    image: "https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?w=300&h=300&fit=crop&crop=face",
    linkedin: "#"
  },
  {
    name: "Kristina Kask",
    role: "Tehnilise juht",
    description: "N8N ekspert ja töövoogude arhitekt",
    image: "https://images.pexels.com/photos/3194521/pexels-photo-3194521.jpeg?w=300&h=300&fit=crop&crop=face",
    linkedin: "#"
  },
  {
    name: "Andres Saar",
    role: "Klienditugi juht",
    description: "Tagab, et iga klient saab parima võimaliku toe",
    image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?w=300&h=300&fit=crop&crop=face",
    linkedin: "#"
  }
];

const stats = [
  { number: 500, label: "Rahulolev klient", suffix: "+" },
  { number: 50, label: "Valmis malli", suffix: "+" },
  { number: 10000, label: "Automatiseeritud ülesanne kuus", suffix: "+" },
  { number: 99, label: "Kliendi rahulolu", suffix: "%" }
];

const colorClasses = {
  cyan: "bg-cyan-500/10 border-cyan-500/30 text-cyan-300",
  green: "bg-green-500/10 border-green-500/30 text-green-300",
  purple: "bg-purple-500/10 border-purple-500/30 text-purple-300",
  orange: "bg-orange-500/10 border-orange-500/30 text-orange-300"
};

export default function AboutPage() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
        {/* Background Effects */}
        <div className="fixed inset-0 z-0">
          <ParticleSystem />
          <AnimatedPipes />
        </div>

        {/* Content */}
        <div className="relative z-10 pt-20">
          {/* Hero Section */}
          <section className="py-20 px-4">
            <div className="container mx-auto text-center">
              <Badge className="mb-6 bg-cyan-500/10 border-cyan-500/20 text-cyan-300">
                Meist
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                <span className="bg-gradient-to-r from-white via-cyan-200 to-green-400 bg-clip-text text-transparent">
                  Automatiseerime Eesti ärisid
                </span>
                <br />
                <span className="text-cyan-400">
                  juba 2020. aastast
                </span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                Oleme Eesti esimene ettevõte, mis spetsialiseerub N8N töövoogude loomisele 
                väikettevõtjatele ja vabakutselistele. Meie missioon on muuta automatiseerimine 
                kättesaadavaks igaühele.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-cyan-400 mb-2">
                      <AnimatedCounter end={stat.number} duration={2.5} />
                      {stat.suffix}
                    </div>
                    <div className="text-sm text-slate-400">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Story Section */}
          <section className="py-20 px-4">
            <div className="container mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">
                    Meie lugu
                  </h2>
                  <div className="space-y-6 text-slate-300 leading-relaxed">
                    <p>
                      Kõik algas 2020. aastal, kui meie asutaja Marko märkas, et Eesti 
                      väikettevõtjad kulutavad liiga palju aega rutiinsetele ülesannetele. 
                      Samal ajal olid automatiseerimise tööriistad keerulised ja kallid.
                    </p>
                    <p>
                      Avastasime N8N platvormi ja mõistsime, et see võib olla lahendus. 
                      Alustasime esimeste mallide loomisega oma klientidele ja tulemused 
                      olid üllatavad - kliendid säästsid keskmiselt 20+ tundi nädalas!
                    </p>
                    <p>
                      Täna oleme Eesti juhtiv N8N automatiseerimise teenusepakkuja, 
                      kes on aidanud üle 500 ettevõtte oma äriprotsesse automatiseerida.
                    </p>
                  </div>
                </div>
                
                <Card className="bg-slate-800/50 border-slate-700/50 p-8 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-green-500/5"></div>
                  <div className="relative z-10">
                    <Lightbulb className="w-12 h-12 text-yellow-400 mb-6" />
                    <h3 className="text-2xl font-bold text-white mb-4">Meie visioon</h3>
                    <p className="text-slate-300 leading-relaxed">
                      Näeme maailma, kus iga Eesti ettevõtja saab keskenduda sellele, 
                      mida ta kõige paremini oskab, samas kui rutiinsed ülesanded 
                      toimuvad automaatselt taustal.
                    </p>
                  </div>
                </Card>
              </div>
            </div>
          </section>

          {/* Values Section */}
          <section className="py-20 px-4">
            <div className="container mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Meie väärtused
                </h2>
                <p className="text-slate-300 text-lg max-w-2xl mx-auto">
                  Need põhimõtted juhivad meid iga päev ja iga otsuse juures
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                {values.map((value, index) => {
                  const IconComponent = value.icon;
                  const colorClass = colorClasses[value.color as keyof typeof colorClasses];
                  
                  return (
                    <Card 
                      key={index}
                      className="bg-slate-800/50 border-slate-700/50 p-8 hover:bg-slate-800/80 transition-all duration-300 group"
                    >
                      <div className="flex items-start gap-4">
                        <div className={`p-3 rounded-lg ${colorClass} border flex-shrink-0`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white mb-3 group-hover:text-cyan-300 transition-colors">
                            {value.title}
                          </h3>
                          <p className="text-slate-300 leading-relaxed">
                            {value.description}
                          </p>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>
          </section>

          {/* Team Section */}
          <section className="py-20 px-4">
            <div className="container mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Meie meeskond
                </h2>
                <p className="text-slate-300 text-lg max-w-2xl mx-auto">
                  Kogemustega spetsialistid, kes on pühendunud teie edu saavutamisele
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                {team.map((member, index) => (
                  <Card 
                    key={index}
                    className="bg-slate-800/50 border-slate-700/50 p-8 text-center hover:bg-slate-800/80 transition-all duration-300 group"
                  >
                    <div className="relative mb-6">
                      <img 
                        src={member.image} 
                        alt={member.name}
                        className="w-24 h-24 rounded-full mx-auto object-cover border-4 border-slate-700/50 group-hover:border-cyan-500/50 transition-colors"
                      />
                      <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-green-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                      {member.name}
                    </h3>
                    <p className="text-cyan-400 font-medium mb-4">{member.role}</p>
                    <p className="text-slate-300 text-sm leading-relaxed mb-6">
                      {member.description}
                    </p>
                    
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="border-slate-600/50 text-slate-300 hover:border-cyan-500/50 hover:text-cyan-300"
                      asChild
                    >
                      <a href={member.linkedin} target="_blank" rel="noopener noreferrer">
                        <Linkedin className="w-4 h-4 mr-2" />
                        LinkedIn
                      </a>
                    </Button>
                  </Card>
                ))}
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="py-20 px-4">
            <div className="container mx-auto">
              <Card className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border-slate-600/50 p-8 md:p-12 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-green-500/5"></div>
                <div className="relative z-10">
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                    Valmis oma äri automatiseerima?
                  </h2>
                  <p className="text-slate-300 text-lg mb-8 max-w-2xl mx-auto">
                    Liitu üle 500 rahuloleva kliendiga, kes on juba oma äri autopiloodi pannud
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button 
                      size="lg"
                      className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-cyan-500/25 group"
                      asChild
                    >
                      <a href="/store">
                        Vaata malle
                        <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </Button>
                    
                    <Button 
                      size="lg"
                      variant="outline"
                      className="border-green-500/50 text-green-400 hover:bg-green-500/10 hover:border-green-400 px-8 py-4 rounded-xl transition-all duration-300"
                      asChild
                    >
                      <a href="mailto:<EMAIL>">
                        <Mail className="w-5 h-5 mr-2" />
                        Võta ühendust
                      </a>
                    </Button>
                  </div>
                </div>
              </Card>
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
'use client';

import { useState } from 'react';
import { Head<PERSON> } from '@/components/layout/Header';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search,
  Filter,
  Star,
  Download,
  Clock,
  Mail, 
  Database, 
  Calendar, 
  ShoppingCart, 
  MessageSquare, 
  FileText,
  Users,
  BarChart3,
  Webhook,
  Zap,
  ArrowRight,
  CheckCircle2,
  Heart
} from 'lucide-react';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import { useCartHelpers } from '@/components/cart/CartProvider';

const categories = [
  { id: 'all', name: 'Kõik mallid', count: 24 },
  { id: 'email', name: 'E-mail Marketing', count: 6 },
  { id: 'crm', name: 'CRM & Müük', count: 5 },
  { id: 'ecommerce', name: '<PERSON>-kauband<PERSON>', count: 4 },
  { id: 'social', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', count: 3 },
  { id: 'documents', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', count: 4 },
  { id: 'analytics', name: 'Analüütika', count: 2 }
];

const templates = [
  {
    id: 1,
    title: "E-mail Marketing Automaatika",
    description: "Täielik e-mail marketing süsteem automaatsete kampaaniate, järelkontrollide ja segmenteerimisega",
    longDescription: "Professionaalne e-mail marketing lahendus, mis automatiseerib kogu kliendiga suhtlemise protsessi. Sisaldab automaatseid tervituskirju, järelkontrolle, klientide segmenteerimist ja A/B testimist.",
    price: "€29",
    originalPrice: "€49",
    category: "email",
    features: [
      "Automaatsed kampaaniad",
      "Klientide segmenteerimine", 
      "Detailne analüütika",
      "A/B testimine",
      "Järelkontrollid",
      "Personaliseeritud sisu"
    ],
    icon: Mail,
    color: "cyan",
    downloads: 234,
    rating: 4.9,
    popular: false,
    difficulty: "Lihtne",
    setupTime: "5 min",
    integrations: ["Gmail", "Mailchimp", "HubSpot", "Google Sheets"]
  },
  {
    id: 2,
    title: "CRM Täielik Integratsioon",
    description: "Klientide andmete sünkroniseerimine ja automaatne staatuste uuendamine mitme platvormi vahel",
    longDescription: "Võimas CRM automatiseerimise lahendus, mis ühendab kõik teie müügikanalid ja automatiseerib klientide haldamise protsessi. Lead scoring, automaatsed aruanded ja müügitoru haldus.",
    price: "€39",
    originalPrice: "€59",
    category: "crm",
    features: [
      "Multi-platvormi sünkroniseerimine",
      "Automaatsed müügiaruanded", 
      "Lead scoring süsteem",
      "Müügitoru automatiseerimine",
      "Klientide segmenteerimine",
      "ROI jälgimine"
    ],
    icon: Database,
    color: "green",
    downloads: 189,
    rating: 4.8,
    popular: true,
    difficulty: "Keskmine",
    setupTime: "10 min",
    integrations: ["HubSpot", "Pipedrive", "Salesforce", "Slack"]
  },
  {
    id: 3,
    title: "Kalender & Broneerimine Pro",
    description: "Täielik broneerimissüsteem automaatsete kinnituste, meeldetuletuste ja Zoom integratsiooniga",
    longDescription: "Professionaalne broneerimise automatiseerimine, mis haldab kogu kohtumiste protsessi algusest lõpuni. Automaatsed kinnitused, meeldetuletused, ümberplaneerimised ja järelkontrollid.",
    price: "€35",
    originalPrice: "€55",
    category: "calendar",
    features: [
      "Kalendri sünkroniseerimine",
      "Automaatsed meeldetuletused", 
      "Zoom/Teams integratsioon",
      "Ootelistide haldus",
      "Ümberplaneerimised",
      "Kliendi tagasiside kogumine"
    ],
    icon: Calendar,
    color: "purple",
    downloads: 167,
    rating: 4.7,
    popular: false,
    difficulty: "Lihtne",
    setupTime: "7 min",
    integrations: ["Google Calendar", "Zoom", "Calendly", "Outlook"]
  },
  {
    id: 4,
    title: "E-kaubanduse Automaatika",
    description: "Tellimuste töötlemine, varude jälgimine ja klientide teavitamine täiesti automaatselt",
    longDescription: "Täielik e-kaubanduse automatiseerimise pakett, mis haldab kogu müügiprotsessi. Tellimuste töötlemine, varude jälgimine, automaatsed arved ja klientide teavitused.",
    price: "€45",
    originalPrice: "€69",
    category: "ecommerce",
    features: [
      "Tellimuste automaatne töötlus",
      "Varude reaalajas jälgimine", 
      "Klientide automaatsed teavitused",
      "Müügianalüütika",
      "Tagastuste haldus",
      "Lojaalsusprogramm"
    ],
    icon: ShoppingCart,
    color: "orange",
    downloads: 298,
    rating: 4.9,
    popular: false,
    difficulty: "Keeruline",
    setupTime: "15 min",
    integrations: ["Shopify", "WooCommerce", "Stripe", "PayPal"]
  },
  {
    id: 5,
    title: "Sotsiaalmeedia Haldur",
    description: "Automaatne sisu avaldamine, kommentaaride modereerimine ja analüütika kõigis kanalites",
    longDescription: "Professionaalne sotsiaalmeedia automatiseerimine, mis haldab kogu teie online kohalolekut. Sisu planeerimine, avaldamine, kommentaaride jälgimine ja detailne analüütika.",
    price: "€32",
    originalPrice: "€52",
    category: "social",
    features: [
      "Multi-platvormi avaldamine",
      "Sisu automaatne planeerimine", 
      "Hashtag optimeerimine",
      "Kommentaaride modereerimine",
      "Konkurentide jälgimine",
      "ROI analüütika"
    ],
    icon: MessageSquare,
    color: "pink",
    downloads: 145,
    rating: 4.6,
    popular: false,
    difficulty: "Keskmine",
    setupTime: "8 min",
    integrations: ["Facebook", "Instagram", "LinkedIn", "Twitter"]
  },
  {
    id: 6,
    title: "Dokumentide Automaatika",
    description: "Lepingute, arvete ja aruannete automaatne genereerimine ja digitaalne allkirjastamine",
    longDescription: "Täielik dokumentide haldamise süsteem, mis automatiseerib kogu dokumendivoo. PDF-ide genereerimine, digitaalne allkirjastamine, versioonihaldus ja arhiveerimine.",
    price: "€38",
    originalPrice: "€58",
    category: "documents",
    features: [
      "PDF automaatne genereerimine",
      "Digitaalne allkirjastamine", 
      "Mallide dünaamiline täitmine",
      "Versioonihaldus",
      "Automaatne arhiveerimine",
      "Juurdepääsu kontroll"
    ],
    icon: FileText,
    color: "blue",
    downloads: 203,
    rating: 4.8,
    popular: false,
    difficulty: "Keskmine",
    setupTime: "12 min",
    integrations: ["DocuSign", "Google Drive", "Dropbox", "OneDrive"]
  }
];

const colorClasses = {
  cyan: { bg: "bg-cyan-500/20", border: "border-cyan-500/30", text: "text-cyan-300", button: "from-cyan-500 to-cyan-600" },
  green: { bg: "bg-green-500/20", border: "border-green-500/30", text: "text-green-300", button: "from-green-500 to-green-600" },
  purple: { bg: "bg-purple-500/20", border: "border-purple-500/30", text: "text-purple-300", button: "from-purple-500 to-purple-600" },
  orange: { bg: "bg-orange-500/20", border: "border-orange-500/30", text: "text-orange-300", button: "from-orange-500 to-orange-600" },
  pink: { bg: "bg-pink-500/20", border: "border-pink-500/30", text: "text-pink-300", button: "from-pink-500 to-pink-600" },
  blue: { bg: "bg-blue-500/20", border: "border-blue-500/30", text: "text-blue-300", button: "from-blue-500 to-blue-600" }
};

export default function StorePage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<typeof templates[0] | null>(null);
  const { addItem, openCart } = useCartHelpers();

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <>
      <Header />
      <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
        {/* Background Effects */}
        <div className="fixed inset-0 z-0">
          <ParticleSystem />
          <AnimatedPipes />
        </div>

        {/* Content */}
        <div className="relative z-10 pt-20">
          {/* Hero Section */}
          <section className="py-20 px-4">
            <div className="container mx-auto text-center">
              <Badge className="mb-6 bg-cyan-500/10 border-cyan-500/20 text-cyan-300">
                N8N Mallide Pood
              </Badge>
              
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                <span className="bg-gradient-to-r from-white via-cyan-200 to-green-400 bg-clip-text text-transparent">
                  Valmis töövoo mallid
                </span>
                <br />
                <span className="text-cyan-400">
                  iga äri jaoks
                </span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                Professionaalselt loodud N8N automatiseerimise mallid, mis lahendavad kõige 
                levinumad äriprobleemid. Seadista minutitega, kasuta igavesti.
              </p>

              {/* Search */}
              <div className="max-w-md mx-auto mb-8">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder="Otsi malle..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Categories & Templates */}
          <section className="py-10 px-4">
            <div className="container mx-auto">
              {/* Category Filter */}
              <div className="flex flex-wrap gap-4 mb-12 justify-center">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-4 py-2 rounded-lg transition-all duration-300 ${
                      selectedCategory === category.id
                        ? 'bg-cyan-500/20 text-cyan-300 border border-cyan-500/30'
                        : 'bg-slate-800/50 text-slate-300 border border-slate-700/50 hover:bg-slate-700/50'
                    }`}
                  >
                    {category.name} ({category.count})
                  </button>
                ))}
              </div>

              {/* Templates Grid */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredTemplates.map((template) => {
                  const IconComponent = template.icon;
                  const colorClass = colorClasses[template.color as keyof typeof colorClasses];
                  
                  return (
                    <Card 
                      key={template.id}
                      className="bg-slate-800/50 border-slate-700/50 p-6 hover:bg-slate-800/80 transition-all duration-300 hover:scale-105 hover:shadow-xl group relative overflow-hidden cursor-pointer"
                      onClick={() => setSelectedTemplate(template)}
                    >
                      {/* Background Glow */}
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 bg-gradient-to-br from-cyan-500/20 to-green-500/20"></div>
                      
                      {template.popular && (
                        <Badge className="absolute -right-2 -top-2 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rotate-12">
                          Populaarne
                        </Badge>
                      )}

                      <div className="relative z-10">
                        {/* Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div className={`p-3 rounded-lg ${colorClass.bg} ${colorClass.border} border`}>
                            <IconComponent className={`w-6 h-6 ${colorClass.text}`} />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-white">{template.price}</div>
                            <div className="text-sm text-slate-400 line-through">{template.originalPrice}</div>
                          </div>
                        </div>

                        {/* Content */}
                        <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                          {template.title}
                        </h3>
                        <p className="text-slate-300 text-sm mb-4 leading-relaxed line-clamp-2">
                          {template.description}
                        </p>

                        {/* Quick Stats */}
                        <div className="flex items-center gap-4 mb-4 text-sm text-slate-400">
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span>{template.rating}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Download className="w-4 h-4" />
                            <span>{template.downloads}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{template.setupTime}</span>
                          </div>
                        </div>

                        {/* Difficulty Badge */}
                        <Badge 
                          variant="outline" 
                          className={`mb-4 ${
                            template.difficulty === 'Lihtne' ? 'border-green-500/50 text-green-300' :
                            template.difficulty === 'Keskmine' ? 'border-yellow-500/50 text-yellow-300' :
                            'border-red-500/50 text-red-300'
                          }`}
                        >
                          {template.difficulty}
                        </Badge>

                        {/* CTA */}
                        <Button 
                          className={`w-full bg-gradient-to-r ${colorClass.button} hover:opacity-90 text-white transition-all duration-300 group-hover:shadow-lg`}
                          onClick={() => setSelectedTemplate(template)}
                        >
                          Vaata detaile
                        </Button>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>
          </section>

          {/* Template Detail Modal */}
          {selectedTemplate && (
            <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
              <Card className="bg-slate-800 border-slate-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-8">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <div className={`p-4 rounded-lg ${colorClasses[selectedTemplate.color as keyof typeof colorClasses].bg} ${colorClasses[selectedTemplate.color as keyof typeof colorClasses].border} border`}>
                        <selectedTemplate.icon className={`w-8 h-8 ${colorClasses[selectedTemplate.color as keyof typeof colorClasses].text}`} />
                      </div>
                      <div>
                        <h2 className="text-3xl font-bold text-white mb-2">{selectedTemplate.title}</h2>
                        <div className="flex items-center gap-4 text-sm text-slate-400">
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span>{selectedTemplate.rating} ({selectedTemplate.downloads} allalaadimist)</span>
                          </div>
                          <Badge variant="outline" className="border-slate-600 text-slate-300">
                            {selectedTemplate.difficulty}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => setSelectedTemplate(null)}
                      className="text-slate-400 hover:text-white transition-colors"
                    >
                      ✕
                    </button>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-8">
                    {/* Left Column */}
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4">Kirjeldus</h3>
                      <p className="text-slate-300 leading-relaxed mb-6">
                        {selectedTemplate.longDescription}
                      </p>

                      <h3 className="text-xl font-bold text-white mb-4">Funktsioonid</h3>
                      <ul className="space-y-2 mb-6">
                        {selectedTemplate.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2 text-slate-300">
                            <CheckCircle2 className="w-5 h-5 text-green-400 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <h3 className="text-xl font-bold text-white mb-4">Integratsioonid</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedTemplate.integrations.map((integration, index) => (
                          <Badge key={index} variant="outline" className="border-slate-600 text-slate-300">
                            {integration}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Right Column */}
                    <div>
                      <Card className="bg-slate-900/50 border-slate-600/50 p-6 mb-6">
                        <div className="text-center mb-6">
                          <div className="text-4xl font-bold text-white mb-2">{selectedTemplate.price}</div>
                          <div className="text-lg text-slate-400 line-through mb-4">{selectedTemplate.originalPrice}</div>
                          <div className="text-green-400 font-semibold">
                            Säästad {parseInt(selectedTemplate.originalPrice.slice(1)) - parseInt(selectedTemplate.price.slice(1))}€
                          </div>
                        </div>

                        <div className="space-y-4 mb-6">
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Seadistusaeg:</span>
                            <span className="text-white">{selectedTemplate.setupTime}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Raskusaste:</span>
                            <span className="text-white">{selectedTemplate.difficulty}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Allalaadimisi:</span>
                            <span className="text-white">{selectedTemplate.downloads}</span>
                          </div>
                        </div>

                        <Button 
                          className={`w-full bg-gradient-to-r ${colorClasses[selectedTemplate.color as keyof typeof colorClasses].button} hover:opacity-90 text-white py-3 text-lg font-semibold transition-all duration-300 mb-4`}
                          onClick={() => {
                            addItem({
                              id: selectedTemplate.id,
                              title: selectedTemplate.title,
                              description: selectedTemplate.description,
                              price: selectedTemplate.price,
                              originalPrice: selectedTemplate.originalPrice,
                              icon: selectedTemplate.icon,
                              color: selectedTemplate.color,
                              difficulty: selectedTemplate.difficulty,
                              setupTime: selectedTemplate.setupTime,
                              features: selectedTemplate.features,
                              integrations: selectedTemplate.integrations
                            });
                            setSelectedTemplate(null);
                            openCart();
                          }}
                        >
                          Osta mall {selectedTemplate.price}
                          <ArrowRight className="w-5 h-5 ml-2" />
                        </Button>

                        <Button 
                          variant="outline"
                          className="w-full border-slate-600 text-slate-300 hover:bg-slate-700/50"
                        >
                          <Heart className="w-4 h-4 mr-2" />
                          Lisa lemmikutesse
                        </Button>
                      </Card>

                      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                        <h4 className="font-semibold text-green-300 mb-2">30-päevane garantii</h4>
                        <p className="text-sm text-slate-300">
                          Kui mall ei vasta ootustele, tagastame raha küsimata
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>
      </main>
    </>
  );
}
'use client';

import { useState } from 'react';
import { Header } from '@/components/layout/Header';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  Zap,
  CheckCircle2,
  User,
  Building,
  Shield
} from 'lucide-react';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false,
    subscribeNewsletter: true
  });
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      alert('Paroolid ei kattu!');
      return;
    }
    
    if (!formData.agreeToTerms) {
      alert('Palun nõustu kasutustingimustega!');
      return;
    }

    setIsLoading(true);
    
    // Simulate registration
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In real app, handle registration and redirect
    router.push('/dashboard');
  };

  return (
    <>
      <Header />
      <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
        {/* Background Effects */}
        <div className="fixed inset-0 z-0">
          <ParticleSystem />
          <AnimatedPipes />
        </div>

        {/* Content */}
        <div className="relative z-10 pt-20">
          <div className="container mx-auto px-4 py-20">
            <div className="max-w-md mx-auto">
              {/* Header */}
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <Zap className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  Loo oma konto
                </h1>
                <p className="text-slate-300">
                  Alusta automatiseerimist juba täna
                </p>
              </div>

              {/* Registration Form */}
              <Card className="bg-slate-800/50 border-slate-700/50 p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-slate-300">Eesnimi</Label>
                      <div className="relative mt-2">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                        <Input
                          id="firstName"
                          name="firstName"
                          type="text"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          placeholder="Sinu eesnimi"
                          className="pl-10 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-slate-300">Perekonnanimi</Label>
                      <div className="relative mt-2">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                        <Input
                          id="lastName"
                          name="lastName"
                          type="text"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          placeholder="Perekonnanimi"
                          className="pl-10 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-slate-300">E-mail</Label>
                    <div className="relative mt-2">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                        className="pl-10 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="company" className="text-slate-300">Ettevõte (valikuline)</Label>
                    <div className="relative mt-2">
                      <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                      <Input
                        id="company"
                        name="company"
                        type="text"
                        value={formData.company}
                        onChange={handleInputChange}
                        placeholder="Sinu ettevõtte nimi"
                        className="pl-10 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="password" className="text-slate-300">Parool</Label>
                    <div className="relative mt-2">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                      <Input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={handleInputChange}
                        placeholder="Vähemalt 8 tähemärki"
                        className="pl-10 pr-10 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                        required
                        minLength={8}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="confirmPassword" className="text-slate-300">Kinnita parool</Label>
                    <div className="relative mt-2">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        placeholder="Korda parooli"
                        className="pl-10 pr-10 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                      >
                        {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <label className="flex items-start gap-3">
                      <input 
                        type="checkbox" 
                        name="agreeToTerms"
                        checked={formData.agreeToTerms}
                        onChange={handleInputChange}
                        className="mt-1 rounded border-slate-600 bg-slate-700 text-cyan-500 focus:ring-cyan-500"
                        required
                      />
                      <span className="text-slate-300 text-sm">
                        Nõustun{' '}
                        <Link href="/terms" className="text-cyan-400 hover:text-cyan-300 underline">
                          kasutustingimuste
                        </Link>
                        {' '}ja{' '}
                        <Link href="/privacy" className="text-cyan-400 hover:text-cyan-300 underline">
                          privaatsuspoliitikaga
                        </Link>
                      </span>
                    </label>
                    
                    <label className="flex items-start gap-3">
                      <input 
                        type="checkbox" 
                        name="subscribeNewsletter"
                        checked={formData.subscribeNewsletter}
                        onChange={handleInputChange}
                        className="mt-1 rounded border-slate-600 bg-slate-700 text-cyan-500 focus:ring-cyan-500"
                      />
                      <span className="text-slate-300 text-sm">
                        Soovin saada uudiskirja uute mallide ja pakkumiste kohta
                      </span>
                    </label>
                  </div>

                  <Button 
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white py-3 text-lg font-semibold transition-all duration-300 group"
                  >
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                        Loome kontot...
                      </>
                    ) : (
                      <>
                        Loo konto
                        <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                      </>
                    )}
                  </Button>
                </form>

                <div className="mt-6 pt-6 border-t border-slate-700/50 text-center">
                  <p className="text-slate-400">
                    Juba on konto?{' '}
                    <Link href="/login" className="text-cyan-400 hover:text-cyan-300 transition-colors">
                      Logi sisse
                    </Link>
                  </p>
                </div>
              </Card>

              {/* Benefits */}
              <div className="mt-8 space-y-3">
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle2 className="w-5 h-5 text-green-400" />
                  <span className="text-sm">Tasuta konto loomine</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle2 className="w-5 h-5 text-green-400" />
                  <span className="text-sm">Juurdepääs kõigile ostetud mallidele</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle2 className="w-5 h-5 text-green-400" />
                  <span className="text-sm">Eesti keeles tugi ja juhendid</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <Shield className="w-5 h-5 text-purple-400" />
                  <span className="text-sm">Turvaline ja privaatne</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
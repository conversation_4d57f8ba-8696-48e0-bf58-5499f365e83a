'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download,
  Play,
  BookOpen,
  Star,
  Clock,
  Calendar,
  ArrowLeft,
  ExternalLink,
  CheckCircle2,
  FileText,
  Video,
  Code,
  Settings,
  Zap,
  Mail,
  Database,
  ShoppingCart,
  AlertCircle,
  Info,
  Users,
  TrendingUp
} from 'lucide-react';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import Link from 'next/link';
import { useParams } from 'next/navigation';

// Mock template data - in real app this would come from API
const templateData = {
  1: {
    id: 1,
    title: "E-mail Marketing Automaatika",
    description: "Täielik e-mail marketing süsteem automaatsete kampaaniate, järelkontrollide ja segmenteerimisega",
    longDescription: "Professionaalne e-mail marketing lahendus, mis automatiseerib kogu kliendiga suhtlemise protsessi. Sisaldab automaatseid tervituskirju, järelkontrolle, klientide segmenteerimist ja A/B testimist. Ideaalne vabakutselistele ja väikettevõtjatele, kes soovivad oma e-mail turundust automatiseerida.",
    purchaseDate: "2024-03-15",
    price: "€29",
    status: "active",
    downloads: 3,
    lastDownload: "2024-03-20",
    icon: Mail,
    color: "cyan",
    difficulty: "Lihtne",
    setupTime: "5 min",
    version: "2.1",
    category: "Marketing",
    rating: 4.9,
    totalDownloads: 234,
    features: [
      "Automaatsed tervituskirjad uutele tellijatele",
      "Järelkontrollide süsteem potentsiaalsetele klientidele",
      "Klientide segmenteerimine käitumise põhjal",
      "A/B testimine erinevate kirjade jaoks",
      "Detailne analüütika ja aruandlus",
      "Integratsioon populaarsete e-mail teenustega"
    ],
    integrations: ["Gmail", "Mailchimp", "HubSpot", "Google Sheets", "Slack"],
    files: [
      { 
        name: "email-marketing-workflow.json", 
        size: "45 KB", 
        type: "N8N Workflow",
        description: "Põhi töövoog koos kõigi automatiseeritud sammudega"
      },
      { 
        name: "setup-guide-et.pdf", 
        size: "2.1 MB", 
        type: "Juhend",
        description: "Detailne seadistamise juhend eesti keeles"
      },
      { 
        name: "video-tutorial.mp4", 
        size: "125 MB", 
        type: "Video õpetus",
        description: "Samm-sammult video juhend seadistamiseks"
      },
      { 
        name: "email-templates.html", 
        size: "89 KB", 
        type: "E-mail mallid",
        description: "Valmis HTML e-mail mallid erinevate kampaaniate jaoks"
      }
    ],
    changelog: [
      { version: "2.1", date: "2024-03-01", changes: ["Lisatud Slack integratsioon", "Parandatud analüütika"] },
      { version: "2.0", date: "2024-02-15", changes: ["Uus A/B testimise funktsioon", "Täiustatud segmenteerimine"] },
      { version: "1.5", date: "2024-01-20", changes: ["Esimene avalik versioon"] }
    ],
    requirements: [
      "N8N Cloud või self-hosted versioon",
      "Gmail või Mailchimp konto",
      "Google Sheets juurdepääs (valikuline)",
      "Põhilised N8N teadmised (õpetame ka!)"
    ],
    useCases: [
      "Uute klientide tervitamine ja onboarding",
      "Mahajäänud klientide tagasivõitmine",
      "Uudiskirjade automaatne saatmine",
      "Lead nurturing kampaaniad",
      "Klientide rahulolu küsitlused"
    ]
  }
};

const colorClasses = {
  cyan: { 
    bg: "bg-cyan-500/10", 
    border: "border-cyan-500/30", 
    text: "text-cyan-300", 
    icon: "text-cyan-400",
    button: "from-cyan-500 to-cyan-600"
  }
};

export default function TemplatePage() {
  const params = useParams();
  const templateId = parseInt(params.id as string);
  const template = templateData[templateId as keyof typeof templateData];
  const [downloadingFile, setDownloadingFile] = useState<string | null>(null);

  if (!template) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-white mb-4">Mall ei leitud</h1>
            <p className="text-slate-300 mb-8">Otsitav mall ei eksisteeri või sul pole sellele juurdepääsu</p>
            <Button asChild>
              <Link href="/dashboard">Tagasi dashboardi</Link>
            </Button>
          </div>
      </main>
    );
  }

  const handleDownload = async (fileName: string) => {
    setDownloadingFile(fileName);
    // Simulate download
    await new Promise(resolve => setTimeout(resolve, 2000));
    setDownloadingFile(null);
    // In real app, trigger actual download
  };

  const IconComponent = template.icon;
  const colorClass = colorClasses[template.color as keyof typeof colorClasses];

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
        {/* Background Effects */}
        <div className="fixed inset-0 z-0">
          <ParticleSystem />
          <AnimatedPipes />
        </div>

        {/* Content */}
        <div className="relative z-10 pt-20">
          <div className="container mx-auto px-4 py-12">
            {/* Header */}
            <div className="flex items-center gap-4 mb-8">
              <Button 
                variant="outline" 
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700/50"
                asChild
              >
                <Link href="/dashboard">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Tagasi dashboardi
                </Link>
              </Button>
            </div>

            {/* Template Header */}
            <div className="grid lg:grid-cols-3 gap-8 mb-12">
              <div className="lg:col-span-2">
                <div className="flex items-start gap-6 mb-6">
                  <div className={`p-4 rounded-xl ${colorClass.bg} ${colorClass.border} border`}>
                    <IconComponent className={`w-12 h-12 ${colorClass.icon}`} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h1 className="text-3xl md:text-4xl font-bold text-white">{template.title}</h1>
                      <Badge variant="outline" className={`${colorClass.border} ${colorClass.text} border`}>
                        v{template.version}
                      </Badge>
                    </div>
                    <p className="text-slate-300 text-lg mb-4">{template.description}</p>
                    
                    <div className="flex items-center gap-6 text-sm text-slate-400">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{template.rating}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Download className="w-4 h-4" />
                        <span>{template.totalDownloads} allalaadimist</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{template.setupTime} seadistus</span>
                      </div>
                      <Badge variant="outline" className="border-slate-600 text-slate-300">
                        {template.difficulty}
                      </Badge>
                    </div>
                  </div>
                </div>

                <p className="text-slate-300 leading-relaxed">
                  {template.longDescription}
                </p>
              </div>

              {/* Quick Actions */}
              <Card className="bg-slate-800/50 border-slate-700/50 p-6 h-fit">
                <h3 className="text-xl font-bold text-white mb-4">Kiired toimingud</h3>
                <div className="space-y-3">
                  <Button 
                    className={`w-full bg-gradient-to-r ${colorClass.button} hover:opacity-90 text-white`}
                    onClick={() => handleDownload('all-files')}
                    disabled={downloadingFile === 'all-files'}
                  >
                    {downloadingFile === 'all-files' ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                        Laadin alla...
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-2" />
                        Laadi kõik alla
                      </>
                    )}
                  </Button>
                  
                  <Button 
                    variant="outline"
                    className="w-full border-slate-600 text-slate-300 hover:bg-slate-700/50"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Vaata videot
                  </Button>
                  
                  <Button 
                    variant="outline"
                    className="w-full border-slate-600 text-slate-300 hover:bg-slate-700/50"
                  >
                    <BookOpen className="w-4 h-4 mr-2" />
                    Ava juhend
                  </Button>
                </div>

                <div className="mt-6 pt-6 border-t border-slate-700/50">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">Ostetud:</span>
                      <span className="text-white">{template.purchaseDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Sinu allalaadimisi:</span>
                      <span className="text-white">{template.downloads}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Viimane allalaadimine:</span>
                      <span className="text-white">{template.lastDownload}</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Main Content Tabs */}
            <Tabs defaultValue="files" className="space-y-8">
              <TabsList className="bg-slate-800/50 border border-slate-700/50">
                <TabsTrigger value="files" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-300">
                  <FileText className="w-4 h-4 mr-2" />
                  Failid
                </TabsTrigger>
                <TabsTrigger value="features" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-300">
                  <Zap className="w-4 h-4 mr-2" />
                  Funktsioonid
                </TabsTrigger>
                <TabsTrigger value="setup" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-300">
                  <Settings className="w-4 h-4 mr-2" />
                  Seadistamine
                </TabsTrigger>
                <TabsTrigger value="changelog" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-300">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Muudatused
                </TabsTrigger>
              </TabsList>

              {/* Files Tab */}
              <TabsContent value="files" className="space-y-6">
                <div className="grid gap-4">
                  {template.files.map((file, index) => (
                    <Card key={index} className="bg-slate-800/50 border-slate-700/50 p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="p-3 bg-slate-700/50 rounded-lg">
                            {file.type === 'N8N Workflow' && <Code className="w-6 h-6 text-cyan-400" />}
                            {file.type === 'Juhend' && <BookOpen className="w-6 h-6 text-green-400" />}
                            {file.type === 'Video õpetus' && <Video className="w-6 h-6 text-purple-400" />}
                            {file.type === 'E-mail mallid' && <Mail className="w-6 h-6 text-orange-400" />}
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-white">{file.name}</h3>
                            <p className="text-slate-300 text-sm mb-1">{file.description}</p>
                            <p className="text-slate-400 text-sm">{file.type} • {file.size}</p>
                          </div>
                        </div>
                        
                        <Button 
                          className="bg-cyan-500 hover:bg-cyan-400 text-white"
                          onClick={() => handleDownload(file.name)}
                          disabled={downloadingFile === file.name}
                        >
                          {downloadingFile === file.name ? (
                            <>
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                              Laadin...
                            </>
                          ) : (
                            <>
                              <Download className="w-4 h-4 mr-2" />
                              Laadi alla
                            </>
                          )}
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              {/* Features Tab */}
              <TabsContent value="features" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-bold text-white mb-6">Peamised funktsioonid</h3>
                    <ul className="space-y-3">
                      {template.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <CheckCircle2 className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
                          <span className="text-slate-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-bold text-white mb-6">Integratsioonid</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {template.integrations.map((integration, index) => (
                        <div key={index} className="flex items-center gap-2 p-3 bg-slate-700/30 rounded-lg">
                          <ExternalLink className="w-4 h-4 text-slate-400" />
                          <span className="text-slate-300 text-sm">{integration}</span>
                        </div>
                      ))}
                    </div>

                    <h3 className="text-xl font-bold text-white mb-6 mt-8">Kasutusviisid</h3>
                    <ul className="space-y-2">
                      {template.useCases.map((useCase, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full flex-shrink-0 mt-2"></div>
                          <span className="text-slate-300 text-sm">{useCase}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </TabsContent>

              {/* Setup Tab */}
              <TabsContent value="setup" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-8">
                  <Card className="bg-slate-800/50 border-slate-700/50 p-6">
                    <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                      <AlertCircle className="w-5 h-5 text-orange-400" />
                      Nõuded
                    </h3>
                    <ul className="space-y-3">
                      {template.requirements.map((requirement, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <Info className="w-4 h-4 text-blue-400 flex-shrink-0 mt-0.5" />
                          <span className="text-slate-300 text-sm">{requirement}</span>
                        </li>
                      ))}
                    </ul>
                  </Card>

                  <Card className="bg-green-950/20 border-green-500/30 p-6">
                    <h3 className="text-xl font-bold text-green-300 mb-4">Seadistamise sammud</h3>
                    <ol className="space-y-3">
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-500/20 border border-green-500/30 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-green-400 text-sm font-bold">1</span>
                        </div>
                        <span className="text-slate-300 text-sm">Laadi alla N8N workflow fail</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-500/20 border border-green-500/30 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-green-400 text-sm font-bold">2</span>
                        </div>
                        <span className="text-slate-300 text-sm">Impordi N8N-i ja seadista ühendused</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-500/20 border border-green-500/30 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-green-400 text-sm font-bold">3</span>
                        </div>
                        <span className="text-slate-300 text-sm">Testi workflow'i ja aktiveeri</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-500/20 border border-green-500/30 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-green-400 text-sm font-bold">4</span>
                        </div>
                        <span className="text-slate-300 text-sm">Naudi automaatset töötamist!</span>
                      </li>
                    </ol>
                  </Card>
                </div>
              </TabsContent>

              {/* Changelog Tab */}
              <TabsContent value="changelog" className="space-y-6">
                <div className="space-y-6">
                  {template.changelog.map((version, index) => (
                    <Card key={index} className="bg-slate-800/50 border-slate-700/50 p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-bold text-white">Versioon {version.version}</h3>
                        <Badge variant="outline" className="border-slate-600 text-slate-300">
                          {version.date}
                        </Badge>
                      </div>
                      <ul className="space-y-2">
                        {version.changes.map((change, changeIndex) => (
                          <li key={changeIndex} className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full flex-shrink-0 mt-2"></div>
                            <span className="text-slate-300 text-sm">{change}</span>
                          </li>
                        ))}
                      </ul>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
    </main>
  );
}
'use client';

import { Alert<PERSON>riangle, CheckCircle2, ArrowR<PERSON> } from 'lucide-react';
import { Card } from '@/components/ui/card';

export function ProblemSolutionSection() {
  return (
    <section className="py-20 px-4 relative">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            <span className="text-red-400">Probleem:</span> Liiga palju käsitööd
          </h2>
          <p className="text-slate-300 text-lg max-w-2xl mx-auto">
            Igapäev kulub tundide kaupa samade ülesannete kordamisele. 
            Aeg, mida saaksid kasutada äri kasvatamiseks.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 items-center mb-20">
          {/* Problem Side */}
          <Card className="bg-red-950/20 border-red-500/30 p-8 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-transparent"></div>
            <div className="relative z-10">
              <div className="flex items-center gap-3 mb-6">
                <AlertTriangle className="w-8 h-8 text-red-400" />
                <h3 className="text-2xl font-bold text-red-300">Enne Autopiloot</h3>
              </div>
              
              <ul className="space-y-4">
                {[
                  "Sama e-kirja saatmine kümneid kordi päevas",
                  "Andmete käsitsi ülekandmine süsteemide vahel",
                  "Klientide staatuste jälgimine Exceli tabelites",
                  "Arvete koostamine ükshaaval",
                  "Sotsiaalmeedia postituste käsitsi programmeerimine"
                ].map((item, index) => (
                  <li key={index} className="flex items-center gap-3 text-slate-300">
                    <div className="w-2 h-2 bg-red-400 rounded-full flex-shrink-0"></div>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-6 p-4 bg-red-500/10 rounded-lg border border-red-500/20">
                <p className="text-red-300 font-semibold">Tulemus: 20+ tundi nädalas rutiini</p>
              </div>
            </div>
          </Card>

          {/* Arrow */}
          <div className="flex justify-center lg:justify-start">
            <ArrowRight className="w-12 h-12 text-cyan-400 animate-pulse" />
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 items-center">
          {/* Solution Side */}
          <Card className="bg-green-950/20 border-green-500/30 p-8 relative overflow-hidden lg:col-start-2">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent"></div>
            <div className="relative z-10">
              <div className="flex items-center gap-3 mb-6">
                <CheckCircle2 className="w-8 h-8 text-green-400" />
                <h3 className="text-2xl font-bold text-green-300">Pärast Autopiloot</h3>
              </div>
              
              <ul className="space-y-4">
                {[
                  "Automaatsed e-kirjad ja järelkontrollid",
                  "Andmete sünkroniseerimine reaalajas",
                  "Automaatne klientide staatuste uuendamine",
                  "Arvete genereerimine ja saatmine",
                  "Sotsiaalmeedia sisu automaatne avaldamine"
                ].map((item, index) => (
                  <li key={index} className="flex items-center gap-3 text-slate-300">
                    <CheckCircle2 className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-6 p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                <p className="text-green-300 font-semibold">Tulemus: 2 tundi seadistust, igavene ajakokkuhoid</p>
              </div>
            </div>
          </Card>

          {/* Solution Title */}
          <div className="lg:col-start-1 lg:row-start-1 text-center lg:text-right">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              <span className="text-green-400">Lahendus:</span> Täielik automaatika
            </h2>
            <p className="text-slate-300 text-lg">
              Valmis N8N töövoo mallid, mis automatiseerivad kõik rutiinsed ülesanded. 
              Seadista kord, kasuta igavesti.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
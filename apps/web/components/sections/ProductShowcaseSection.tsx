'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Mail, 
  Database, 
  Calendar, 
  ShoppingCart, 
  MessageSquare, 
  FileText, 
  Star,
  Download,
  Clock
} from 'lucide-react';

const templates = [
  {
    id: 1,
    title: "E-mail Marketing Automaatika",
    description: "Automaatsed uudiskirjad, järelkontrollid ja klientide segmenteerimine",
    price: "€29",
    originalPrice: "€49",
    features: ["Automaatsed kampaaniad", "Klientide segmenteerimine", "Analüütika", "A/B testimine"],
    icon: Mail,
    color: "cyan",
    downloads: 234,
    rating: 4.9,
    popular: false
  },
  {
    id: 2,
    title: "CRM Integratsioon",
    description: "Klientide andmete sünkroniseerimine ja automaatne staatuste uuendamine",
    price: "€39",
    originalPrice: "€59",
    features: ["Multi-platvormi sünk", "Automaatsed aruanded", "Lead scoring", "Päästikud"],
    icon: Database,
    color: "green",
    downloads: 189,
    rating: 4.8,
    popular: true
  },
  {
    id: 3,
    title: "Kalender & Broneerimine",
    description: "Automaatne kohtumiste broneerimine ja kinnituste saatmine",
    price: "€35",
    originalPrice: "€55",
    features: ["Kalendri sünk", "Automaatsed meeldetuletused", "Zoom integratsioon", "Ootelistid"],
    icon: Calendar,
    color: "purple",
    downloads: 167,
    rating: 4.7,
    popular: false
  },
  {
    id: 4,
    title: "E-kaubanduse Automaatika",
    description: "Tellimuste töötlemine ja varude jälgimine automaatselt",
    price: "€45",
    originalPrice: "€69",
    features: ["Tellimuste töötlus", "Varude haldus", "Klientide teavitused", "Analüütika"],
    icon: ShoppingCart,
    color: "orange",
    downloads: 298,
    rating: 4.9,
    popular: false
  },
  {
    id: 5,
    title: "Sotsiaalmeedia Haldur",
    description: "Automaatne sisu avaldamine ja kommentaaride modereerimine",
    price: "€32",
    originalPrice: "€52",
    features: ["Multi-platvormi postitus", "Sisu planeerimine", "Hashtag optimeerimine", "Statistika"],
    icon: MessageSquare,
    color: "pink",
    downloads: 145,
    rating: 4.6,
    popular: false
  },
  {
    id: 6,
    title: "Dokumentide Automaatika",
    description: "Lepingute, arvete ja aruannete automaatne genereerimine",
    price: "€38",
    originalPrice: "€58",
    features: ["PDF genereerimine", "Digitaalne allkiri", "Mallide haldus", "Versioonihaldus"],
    icon: FileText,
    color: "blue",
    downloads: 203,
    rating: 4.8,
    popular: false
  }
];

const colorClasses = {
  cyan: "bg-cyan-500/20 border-cyan-500/30 text-cyan-300",
  green: "bg-green-500/20 border-green-500/30 text-green-300",
  purple: "bg-purple-500/20 border-purple-500/30 text-purple-300",
  orange: "bg-orange-500/20 border-orange-500/30 text-orange-300",
  pink: "bg-pink-500/20 border-pink-500/30 text-pink-300",
  blue: "bg-blue-500/20 border-blue-500/30 text-blue-300"
};

export function ProductShowcaseSection() {
  return (
    <section className="py-20 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-cyan-400 to-green-400 bg-clip-text text-transparent">
              Valmis töövoo mallid
            </span>
          </h2>
          <p className="text-slate-300 text-lg max-w-3xl mx-auto mb-8">
            Professionaalselt loodud N8N automatiseerimise mallid, mis lahendavad kõige levinumad äriprobleemid. 
            Seadista minutitega, kasuta igavesti.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4">
            <Badge variant="outline" className="border-cyan-500/50 text-cyan-300">
              Kiire seadistus
            </Badge>
            <Badge variant="outline" className="border-green-500/50 text-green-300">
              Eesti tugi
            </Badge>
            <Badge variant="outline" className="border-purple-500/50 text-purple-300">
              30 päeva raha tagasi
            </Badge>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {templates.map((template) => {
            const IconComponent = template.icon;
            const colorClass = colorClasses[template.color as keyof typeof colorClasses];
            
            return (
              <Card 
                key={template.id}
                className="bg-slate-800/50 border-slate-700/50 p-6 hover:bg-slate-800/80 transition-all duration-300 hover:scale-105 hover:shadow-xl group relative overflow-hidden"
              >
                {/* Background Glow */}
                <div className={`absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 bg-gradient-to-br from-${template.color}-500/20 to-transparent`}></div>
                
                {template.popular && (
                  <Badge className="absolute -right-2 -top-2 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rotate-12">
                    Populaarne
                  </Badge>
                )}

                <div className="relative z-10">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg ${colorClass} border`}>
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-white">{template.price}</div>
                      <div className="text-sm text-slate-400 line-through">{template.originalPrice}</div>
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                    {template.title}
                  </h3>
                  <p className="text-slate-300 text-sm mb-4 leading-relaxed">
                    {template.description}
                  </p>

                  {/* Features */}
                  <ul className="space-y-1 mb-6">
                    {template.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm text-slate-400">
                        <div className={`w-1.5 h-1.5 rounded-full bg-${template.color}-400`}></div>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {/* Stats */}
                  <div className="flex items-center gap-4 mb-6 text-sm text-slate-400">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span>{template.rating}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="w-4 h-4" />
                      <span>{template.downloads}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>5 min</span>
                    </div>
                  </div>

                  {/* CTA */}
                  <Button 
                    className={`w-full bg-gradient-to-r from-${template.color}-500 to-${template.color}-600 hover:from-${template.color}-400 hover:to-${template.color}-500 text-white transition-all duration-300 group-hover:shadow-lg`}
                  >
                    Osta mall
                  </Button>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-600/50 rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ei leia sobivat malli?
            </h3>
            <p className="text-slate-300 mb-6">
              Loome sulle personaalse automatiseerimise lahenduse. Konsultatsioon on tasuta!
            </p>
            <Button 
              size="lg"
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-400 hover:to-pink-400 text-white px-8 py-3 rounded-xl transition-all duration-300"
            >
              Telli kohandatud lahendus
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
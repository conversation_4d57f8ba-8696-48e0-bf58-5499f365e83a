'use client';

import { Clock, TrendingUp, Shield, Users, Zap, Target } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { AnimatedCounter } from '@/components/animations/AnimatedCounter';

const benefits = [
  {
    icon: Clock,
    title: "Rohkem aega",
    description: "Automatiseeri rutiinsed ülesanded ja keskendu äri kasvatamisele",
    metric: "20+",
    metricLabel: "tundi nädalas",
    color: "cyan"
  },
  {
    icon: TrendingUp,
    title: "Suurem tulu",
    description: "Rohkem klientidega suhtlemiseks ja uute projektide alustamiseks",
    metric: "150%",
    metricLabel: "tulu kasv",
    color: "green"
  },
  {
    icon: Shield,
    title: "Vähem vigu",
    description: "Automatiseeritud protsessid vähendavad inimlike vigade riski",
    metric: "95%",
    metricLabel: "vähem vigu",
    color: "purple"
  },
  {
    icon: Users,
    title: "<PERSON>rem klienditeenindus",
    description: "<PERSON><PERSON><PERSON> vastused ja professionaalsem suhtlus",
    metric: "4.8/5",
    metricLabel: "kliendi hinnang",
    color: "orange"
  },
  {
    icon: Zap,
    title: "24/7 töötamine",
    description: "Sinu äri töötab ka siis, kui sa magad või puhkad",
    metric: "24/7",
    metricLabel: "automaatne töö",
    color: "pink"
  },
  {
    icon: Target,
    title: "Parem fokus",
    description: "Keskendud strateegilistele otsustele, mitte rutiinile",
    metric: "3x",
    metricLabel: "parem fokus",
    color: "blue"
  }
];

const colorClasses = {
  cyan: {
    bg: "bg-cyan-500/10",
    border: "border-cyan-500/30",
    icon: "text-cyan-400",
    glow: "group-hover:shadow-cyan-500/25"
  },
  green: {
    bg: "bg-green-500/10",
    border: "border-green-500/30",
    icon: "text-green-400",
    glow: "group-hover:shadow-green-500/25"
  },
  purple: {
    bg: "bg-purple-500/10",
    border: "border-purple-500/30",
    icon: "text-purple-400",
    glow: "group-hover:shadow-purple-500/25"
  },
  orange: {
    bg: "bg-orange-500/10",
    border: "border-orange-500/30",
    icon: "text-orange-400",
    glow: "group-hover:shadow-orange-500/25"
  },
  pink: {
    bg: "bg-pink-500/10",
    border: "border-pink-500/30",
    icon: "text-pink-400",
    glow: "group-hover:shadow-pink-500/25"
  },
  blue: {
    bg: "bg-blue-500/10",
    border: "border-blue-500/30",
    icon: "text-blue-400",
    glow: "group-hover:shadow-blue-500/25"
  }
};

export function BenefitsSection() {
  return (
    <section className="py-20 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            Miks valida 
            <span className="bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent">
              {" "}automatiseerimine?
            </span>
          </h2>
          <p className="text-slate-300 text-lg max-w-3xl mx-auto">
            Tuhandeid tunde testimist ja optimeerimist, et tuua sulle parimad tulemused. 
            Vaata, mida saavutavad meie kliendid.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {benefits.map((benefit, index) => {
            const IconComponent = benefit.icon;
            const colorClass = colorClasses[benefit.color as keyof typeof colorClasses];
            
            return (
              <Card 
                key={index}
                className={`${colorClass.bg} ${colorClass.border} border p-8 hover:scale-105 transition-all duration-300 group hover:shadow-xl ${colorClass.glow} relative overflow-hidden`}
              >
                {/* Background Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div className="relative z-10">
                  {/* Icon and Metric */}
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${colorClass.bg} ${colorClass.border} border`}>
                      <IconComponent className={`w-6 h-6 ${colorClass.icon}`} />
                    </div>
                    <div className="text-right">
                      <div className={`text-2xl font-bold ${colorClass.icon}`}>
                        {benefit.metric.includes('%') || benefit.metric.includes('/') || benefit.metric.includes('x') ? 
                          benefit.metric : 
                          <AnimatedCounter end={parseInt(benefit.metric)} duration={2} />
                        }
                      </div>
                      <div className="text-sm text-slate-400">{benefit.metricLabel}</div>
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-cyan-300 transition-colors">
                    {benefit.title}
                  </h3>
                  <p className="text-slate-300 leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Success Stories */}
        <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-600/50 rounded-2xl p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Edu numbrites
            </h3>
            <p className="text-slate-300">
              Meie klientide saavutused pärast automatiseerimist
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-cyan-400 mb-2">
                <AnimatedCounter end={500} duration={3} />+
              </div>
              <div className="text-slate-300">Rahulolev klient</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-400 mb-2">
                <AnimatedCounter end={10000} duration={3} />+
              </div>
              <div className="text-slate-300">Automatiseeritud ülesanne kuus</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-400 mb-2">
                <AnimatedCounter end={89} duration={2.5} />%
              </div>
              <div className="text-slate-300">Ajakokkuhoid</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-orange-400 mb-2">
                <AnimatedCounter end={24} duration={2} />h
              </div>
              <div className="text-slate-300">Keskmine ajakokkuhoid nädalas</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
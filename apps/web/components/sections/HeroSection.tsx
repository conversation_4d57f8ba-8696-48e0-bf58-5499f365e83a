'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, <PERSON>ap, Clock, Target } from 'lucide-react';
import { AnimatedCounter } from '@/components/animations/AnimatedCounter';

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center px-4 py-20">
      {/* Hero Content */}
      <div className="container mx-auto text-center relative z-20">
        {/* Badge */}
        <div className="inline-flex items-center gap-2 bg-cyan-500/10 border border-cyan-500/20 rounded-full px-4 py-2 mb-8">
          <Zap className="w-4 h-4 text-cyan-400" />
          <span className="text-cyan-300 text-sm font-medium">N8N mallid Eesti ettevõtjatele</span>
        </div>

        {/* Main Headline */}
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
          <span className="bg-gradient-to-r from-white via-cyan-200 to-green-400 bg-clip-text text-transparent">
            Pane oma äri
          </span>
          <br />
          <span className="text-cyan-400 drop-shadow-lg">
            autopiloodi
          </span>
        </h1>

        {/* Subheadline */}
        <p className="text-xl md:text-2xl text-slate-300 mb-8 max-w-3xl mx-auto leading-relaxed">
          Automatiseeri igapäevased ülesanded ja keskendu sellele, mis tõeliselt oluline on. 
          Valmis N8N töövoo mallid vabakutselistele ja väikettevõtjatele.
        </p>

        {/* Stats */}
        <div className="flex flex-wrap justify-center gap-8 mb-12">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-1">
              <AnimatedCounter end={24} duration={2} />h
            </div>
            <div className="text-sm text-slate-400">automatiseeritud töö</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-cyan-400 mb-1">
              <AnimatedCounter end={89} duration={2.5} />%
            </div>
            <div className="text-sm text-slate-400">vähem rutiini</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-1">
              <AnimatedCounter end={150} duration={3} />+
            </div>
            <div className="text-sm text-slate-400">rahulolev klient</div>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
          <Button 
            size="lg" 
            className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-cyan-500/25 group"
          >
            Vaata malle
            <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button 
            size="lg" 
            variant="outline" 
            className="border-green-500/50 text-green-400 hover:bg-green-500/10 hover:border-green-400 px-8 py-4 rounded-xl transition-all duration-300"
          >
            <Clock className="w-5 h-5 mr-2" />
            Tasuta demo
          </Button>
        </div>

        {/* Feature Icons */}
        <div className="flex flex-wrap justify-center gap-8 opacity-80">
          <div className="flex items-center gap-2 text-slate-400">
            <Target className="w-5 h-5 text-cyan-400" />
            <span className="text-sm">Kiire seadistus</span>
          </div>
          <div className="flex items-center gap-2 text-slate-400">
            <Zap className="w-5 h-5 text-green-400" />
            <span className="text-sm">24/7 automaatika</span>
          </div>
          <div className="flex items-center gap-2 text-slate-400">
            <Clock className="w-5 h-5 text-purple-400" />
            <span className="text-sm">Eesti tugi</span>
          </div>
        </div>
      </div>
    </section>
  );
}
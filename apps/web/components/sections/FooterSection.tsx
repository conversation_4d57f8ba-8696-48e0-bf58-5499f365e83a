'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Instagram,
  ArrowRight
} from 'lucide-react';

export function FooterSection() {
  return (
    <footer className="bg-slate-900/80 border-t border-slate-800/50 py-16 px-4 relative">
      <div className="container mx-auto">
        {/* Newsletter Section */}
        <div className="text-center mb-16">
          <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-600/50 rounded-2xl p-8 md:p-12 max-w-2xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Jää kursis automatiseerimise uudistega
            </h3>
            <p className="text-slate-300 mb-8">
              Saa esimesena teada uutest mallidest, näpunäidetest ja pakkumistest
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input 
                type="email" 
                placeholder="Sisesta oma e-mail"
                className="bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400 flex-1"
              />
              <Button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-6 group">
                Liitu
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Autopiloot</h2>
              <p className="text-slate-300 leading-relaxed">
                Automatiseeri oma äri 24/7. Professionaalsed N8N töövoo mallid 
                Eesti vabakutselistele ja väikettevõtjatele. Rohkem aega, vähem rutiini.
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-slate-300">
                <Mail className="w-5 h-5 text-cyan-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-slate-300">
                <Phone className="w-5 h-5 text-green-400" />
                <span>+372 5555 5555</span>
              </div>
              <div className="flex items-center gap-3 text-slate-300">
                <MapPin className="w-5 h-5 text-purple-400" />
                <span>Tallinn, Eesti</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Kiirlinked</h3>
            <ul className="space-y-3">
              {[
                "Kõik mallid",
                "Populaarsed mallid",
                "Tasuta mallid",
                "Kohandatud lahendused",
                "Õpetused",
                "Blogi"
              ].map((link, index) => (
                <li key={index}>
                  <a 
                    href="#" 
                    className="text-slate-300 hover:text-cyan-300 transition-colors"
                  >
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Tugi</h3>
            <ul className="space-y-3">
              {[
                "Dokumentatsioon",
                "KKK",
                "Live Chat",
                "Kogukond",
                "Kontakt",
                "Tagasiside"
              ].map((link, index) => (
                <li key={index}>
                  <a 
                    href="#" 
                    className="text-slate-300 hover:text-cyan-300 transition-colors"
                  >
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-slate-800/50 mt-16 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            {/* Copyright */}
            <div className="text-slate-400 text-sm">
              © 2025 Autopiloot. Kõik õigused kaitstud.
            </div>

            {/* Legal Links */}
            <div className="flex gap-6 text-sm">
              <a href="#" className="text-slate-400 hover:text-cyan-300 transition-colors">
                Privaatsuspoliitika
              </a>
              <a href="#" className="text-slate-400 hover:text-cyan-300 transition-colors">
                Kasutustingimused
              </a>
              <a href="#" className="text-slate-400 hover:text-cyan-300 transition-colors">
                Küpsised
              </a>
            </div>

            {/* Social Links */}
            <div className="flex gap-4">
              {[
                { icon: Facebook, href: "#", label: "Facebook" },
                { icon: Twitter, href: "#", label: "Twitter" },
                { icon: Linkedin, href: "#", label: "LinkedIn" },
                { icon: Instagram, href: "#", label: "Instagram" }
              ].map(({ icon: Icon, href, label }, index) => (
                <a 
                  key={index}
                  href={href}
                  aria-label={label}
                  className="w-10 h-10 bg-slate-800/50 border border-slate-700/50 rounded-lg flex items-center justify-center text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-300"
                >
                  <Icon className="w-5 h-5" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
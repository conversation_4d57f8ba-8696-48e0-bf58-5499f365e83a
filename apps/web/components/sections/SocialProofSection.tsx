'use client';

import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star, Quote } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "Vabakutseline disainer",
    company: "Creative Studio",
    image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    text: "Autopiloot muutis mu äri täiesti! Klientidega suhtlemise ja projektide haldamise automatiseerimine säästs mulle nädalas üle 20 tunni. Nüüd saan keskenduda sellele, mida tõeliselt armastan - disainimisele.",
    metric: "20h nädalas kokku hoitud"
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "E-kaubanduse omanik",
    company: "Fashion House",
    image: "https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    text: "Tellimuste töötlemine ja klientide teavitamine toimub nüüd automaatselt. Minu müügikäive kasvas 150%, sest saan rohkem aega pühendada toodete arendamisele ja turundusele.",
    metric: "150% müügikasv"
  },
  {
    id: 3,
    name: "Andres Kask",
    role: "Konsultant",
    company: "Business Solutions",
    image: "https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    text: "Klientidele pakkumiste saatmine ja järelkontrollide tegemine toimub nüüd täiesti automaatselt. Saan rohkem projekte vastu võtta ja teenin märkimisväärselt rohkem.",
    metric: "3x rohkem projekte"
  },
  {
    id: 4,
    name: "Maria Saar",
    role: "Turundusekspert",
    company: "Digital Marketing",
    image: "https://images.pexels.com/photos/3194521/pexels-photo-3194521.jpeg?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    text: "Sotsiaalmeedia sisu planeerimine ja avaldamine toimub automaatselt. Kampaaniate tulemused on paremad ja mul on rohkem aega strateegilisteks otsusteks.",
    metric: "89% parem efektiivsus"
  },
  {
    id: 5,
    name: "Toomas Mets",
    role: "IT-konsultant",
    company: "Tech Solutions",
    image: "https://images.pexels.com/photos/2381069/pexels-photo-2381069.jpeg?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    text: "Klientide tehnilise toe automatiseerimine on uskumatu! Peaaegu kõik tavalised küsimused saavad vastuse automaatselt. Kliendid on rahulolus ja mina ei pea 24/7 arvuti juures istuma.",
    metric: "24/7 klienditugi"
  },
  {
    id: 6,
    name: "Elena Kivi",
    role: "Koolitaja",
    company: "Learning Academy",
    image: "https://images.pexels.com/photos/3727463/pexels-photo-3727463.jpeg?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    text: "Kursuste müümine, õppurite registreerimine ja sertifikaatide väljastamine toimub automaatselt. Saan keskenduda kvaliteetsete õppematerjalide loomisele.",
    metric: "Täielik automatiseerimine"
  }
];

const logos = [
  { name: "Estonian Business", logo: "EB" },
  { name: "Startup Estonia", logo: "SE" },
  { name: "Digital Estonia", logo: "DE" },
  { name: "Tech Hub", logo: "TH" },
  { name: "Innovation Lab", logo: "IL" },
  { name: "Future Works", logo: "FW" }
];

export function SocialProofSection() {
  return (
    <section className="py-20 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            Mida ütlevad 
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}meie kliendid?
            </span>
          </h2>
          <p className="text-slate-300 text-lg max-w-3xl mx-auto">
            Üle 500 rahuloleva kliendi on juba oma äri automatiseerinud. 
            Vaata, mida nemad ütlevad!
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial) => (
            <Card 
              key={testimonial.id}
              className="bg-slate-800/50 border-slate-700/50 p-6 hover:bg-slate-800/80 transition-all duration-300 hover:scale-105 group relative overflow-hidden"
            >
              {/* Quote Icon */}
              <Quote className="absolute top-4 right-4 w-8 h-8 text-cyan-400/20" />
              
              {/* Rating */}
              <div className="flex items-center gap-1 mb-4">
                {Array.from({ length: testimonial.rating }).map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                ))}
              </div>

              {/* Testimonial Text */}
              <p className="text-slate-300 mb-6 leading-relaxed">
                "{testimonial.text}"
              </p>

              {/* Metric */}
              <div className="bg-gradient-to-r from-cyan-500/10 to-green-500/10 border border-cyan-500/20 rounded-lg p-3 mb-4">
                <div className="text-cyan-300 font-semibold text-sm">
                  📈 {testimonial.metric}
                </div>
              </div>

              {/* Author */}
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={testimonial.image} alt={testimonial.name} />
                  <AvatarFallback className="bg-slate-700 text-white">
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-semibold text-white">{testimonial.name}</div>
                  <div className="text-sm text-slate-400">
                    {testimonial.role}, {testimonial.company}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Company Logos */}
        <div className="text-center">
          <p className="text-slate-400 mb-8">Usaldavad ettevõtted üle Eesti</p>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {logos.map((company, index) => (
              <div 
                key={index}
                className="flex items-center justify-center w-16 h-16 bg-slate-800/50 border border-slate-600/50 rounded-lg hover:bg-slate-700/50 transition-colors"
              >
                <span className="text-slate-300 font-bold text-sm">
                  {company.logo}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
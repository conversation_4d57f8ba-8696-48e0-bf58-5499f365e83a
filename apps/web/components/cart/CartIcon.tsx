'use client';

import { ShoppingCart } from 'lucide-react';
import { useCartHelpers } from './CartProvider';

export function CartIcon() {
  const { getTotalItems, toggleCart } = useCartHelpers();
  const itemCount = getTotalItems();

  return (
    <button
      onClick={toggleCart}
      className="relative p-2 rounded-lg bg-slate-800/50 border border-slate-700/50 text-slate-300 hover:text-white hover:bg-slate-700/50 transition-all duration-300 group"
    >
      <ShoppingCart className="w-5 h-5 group-hover:scale-110 transition-transform" />
      {itemCount > 0 && (
        <>
          <span className="absolute -top-2 -right-2 bg-gradient-to-r from-cyan-500 to-blue-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center animate-pulse">
            {itemCount > 9 ? '9+' : itemCount}
          </span>
          <div className="absolute -top-2 -right-2 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-full w-6 h-6 animate-ping opacity-30"></div>
        </>
      )}
    </button>
  );
}
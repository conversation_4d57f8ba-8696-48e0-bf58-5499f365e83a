'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  Plus, 
  Minus, 
  ShoppingCart, 
  ArrowRight,
  Trash2
} from 'lucide-react';
import { useCartHelpers } from './CartProvider';
import Link from 'next/link';

export function CartDrawer() {
  const {
    items,
    isOpen,
    removeItem,
    updateQuantity,
    closeCart,
    getTotalItems,
    getTotalPrice,
    getSavings,
  } = useCartHelpers();

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
        onClick={closeCart}
      />
      
      {/* Cart Drawer */}
      <div className="fixed right-0 top-0 h-full w-full max-w-md bg-slate-900/95 backdrop-blur-xl border-l border-slate-700/50 z-50 transform transition-transform duration-300 ease-in-out shadow-2xl">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-slate-700/50 bg-slate-800/30">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-cyan-500/20 border border-cyan-500/30 rounded-lg">
                <ShoppingCart className="w-5 h-5 text-cyan-400" />
              </div>
              <h2 className="text-xl font-bold text-white">
                Ostukorv ({getTotalItems()})
              </h2>
            </div>
            <button
              onClick={closeCart}
              className="p-2 rounded-lg bg-slate-800/50 border border-slate-700/50 text-slate-400 hover:text-white hover:bg-slate-700/50 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {items.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-slate-800/50 border border-slate-700/50 rounded-full flex items-center justify-center mx-auto mb-6">
                  <ShoppingCart className="w-10 h-10 text-slate-600" />
                </div>
                <h3 className="text-lg font-semibold text-slate-300 mb-2">
                  Ostukorv on tühi
                </h3>
                <p className="text-slate-500 mb-6">
                  Lisa malle ostukorvi, et alustada automatiseerimist
                </p>
                <Button 
                  onClick={closeCart}
                  className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white"
                  asChild
                >
                  <Link href="/store">
                    Vaata malle
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item) => {
                  const IconComponent = item.icon;
                  
                  return (
                    <Card key={item.id} className="bg-slate-800/50 border-slate-700/50 p-4 hover:bg-slate-800/70 transition-colors">
                      <div className="flex items-start gap-4">
                        <div className="p-2 rounded-lg bg-cyan-500/10 border border-cyan-500/20">
                          <IconComponent className="w-5 h-5 text-cyan-400" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-white text-sm mb-1 truncate">
                            {item.title}
                          </h3>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-lg font-bold text-white">
                              {item.price}
                            </span>
                            <span className="text-sm text-slate-400 line-through">
                              {item.originalPrice}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="border-slate-600 text-slate-300 text-xs">
                              {item.difficulty}
                            </Badge>
                            <span className="text-xs text-slate-400">
                              {item.setupTime}
                            </span>
                          </div>
                        </div>

                        <div className="flex flex-col items-end gap-2">
                          <button
                            onClick={() => removeItem(item.id)}
                            className="p-1 rounded text-slate-400 hover:text-red-400 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                          
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="p-1 rounded bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 transition-colors"
                              disabled={item.quantity <= 1}
                            >
                              <Minus className="w-3 h-3" />
                            </button>
                            <span className="text-white font-medium w-8 text-center">
                              {item.quantity}
                            </span>
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="p-1 rounded bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 transition-colors"
                            >
                              <Plus className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          {items.length > 0 && (
            <div className="border-t border-slate-700/50 p-6 bg-slate-800/30">
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-slate-300">
                  <span>Vahesumma:</span>
                  <span>€{getTotalPrice().toFixed(2)}</span>
                </div>
                {getSavings() > 0 && (
                  <div className="flex justify-between text-green-400">
                    <span>Kokkuhoid:</span>
                    <span>-€{getSavings().toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between text-xl font-bold text-white border-t border-slate-700/50 pt-3">
                  <span>Kokku:</span>
                  <span>€{getTotalPrice().toFixed(2)}</span>
                </div>
              </div>

              <Button 
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 text-white py-3 text-lg font-semibold transition-all duration-300 group"
                asChild
              >
                <Link href="/checkout" onClick={closeCart}>
                  Jätka ostuga
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>

              <p className="text-xs text-slate-400 text-center mt-3">
                30-päevane raha tagasi garantii
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
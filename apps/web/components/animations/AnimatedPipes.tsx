'use client';

import { useEffect, useRef } from 'react';

export function AnimatedPipes() {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    const svg = svgRef.current;
    if (!svg) return;

    // Create animated particles
    const particles = svg.querySelectorAll('.particle');
    particles.forEach((particle, index) => {
      const element = particle as SVGCircleElement;
      element.style.animationDelay = `${index * 0.5}s`;
    });
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <svg
        ref={svgRef}
        className="absolute inset-0 w-full h-full"
        viewBox="0 0 1920 1080"
        preserveAspectRatio="xMidYMid slice"
      >
        <defs>
          {/* Gradient Definitions */}
          <linearGradient id="pipeGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(0, 212, 255, 0.3)" />
            <stop offset="50%" stopColor="rgba(57, 255, 20, 0.3)" />
            <stop offset="100%" stopColor="rgba(139, 69, 255, 0.3)" />
          </linearGradient>
          
          <linearGradient id="pipeGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(139, 69, 255, 0.2)" />
            <stop offset="50%" stopColor="rgba(0, 212, 255, 0.2)" />
            <stop offset="100%" stopColor="rgba(57, 255, 20, 0.2)" />
          </linearGradient>

          {/* Glow Filters */}
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          <filter id="strongGlow">
            <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          {/* Animation Paths */}
          <path id="mainPath1" d="M-100,200 Q200,150 400,300 T800,250 Q1200,200 1600,350 T2100,300" />
          <path id="mainPath2" d="M-100,600 Q300,550 600,650 T1000,600 Q1400,550 1800,700 T2200,650" />
          <path id="branchPath1" d="M400,300 Q500,200 600,100 T900,50" />
          <path id="branchPath2" d="M800,250 Q900,350 1000,450 T1300,500" />
        </defs>

        {/* Main Horizontal Pipes */}
        <path
          d="M-100,200 Q200,150 400,300 T800,250 Q1200,200 1600,350 T2100,300"
          stroke="url(#pipeGradient1)"
          strokeWidth="12"
          fill="none"
          filter="url(#glow)"
          className="opacity-60"
        />
        
        <path
          d="M-100,600 Q300,550 600,650 T1000,600 Q1400,550 1800,700 T2200,650"
          stroke="url(#pipeGradient2)"
          strokeWidth="8"
          fill="none"
          filter="url(#glow)"
          className="opacity-40"
        />

        {/* Branching Pipes */}
        <path
          d="M400,300 Q500,200 600,100 T900,50"
          stroke="rgba(0, 212, 255, 0.4)"
          strokeWidth="6"
          fill="none"
          filter="url(#glow)"
          className="opacity-50"
        />
        
        <path
          d="M800,250 Q900,350 1000,450 T1300,500"
          stroke="rgba(57, 255, 20, 0.4)"
          strokeWidth="6"
          fill="none"
          filter="url(#glow)"
          className="opacity-50"
        />

        {/* Vertical Connecting Pipes */}
        <line
          x1="600"
          y1="100"
          x2="600"
          y2="650"
          stroke="rgba(139, 69, 255, 0.3)"
          strokeWidth="4"
          filter="url(#glow)"
          className="opacity-30"
        />
        
        <line
          x1="1000"
          y1="450"
          x2="1000"
          y2="600"
          stroke="rgba(0, 212, 255, 0.3)"
          strokeWidth="4"
          filter="url(#glow)"
          className="opacity-30"
        />

        {/* Animated Particles/Data Flow */}
        <circle className="particle" r="4" fill="rgba(0, 212, 255, 0.8)" filter="url(#strongGlow)">
          <animateMotion dur="8s" repeatCount="indefinite">
            <mpath xlinkHref="#mainPath1" />
          </animateMotion>
        </circle>
        
        <circle className="particle" r="3" fill="rgba(57, 255, 20, 0.8)" filter="url(#strongGlow)">
          <animateMotion dur="10s" repeatCount="indefinite">
            <mpath xlinkHref="#mainPath2" />
          </animateMotion>
        </circle>
        
        <circle className="particle" r="2" fill="rgba(139, 69, 255, 0.8)" filter="url(#glow)">
          <animateMotion dur="6s" repeatCount="indefinite">
            <mpath xlinkHref="#branchPath1" />
          </animateMotion>
        </circle>
        
        <circle className="particle" r="3" fill="rgba(255, 165, 0, 0.8)" filter="url(#glow)">
          <animateMotion dur="7s" repeatCount="indefinite">
            <mpath xlinkHref="#branchPath2" />
          </animateMotion>
        </circle>

        {/* Additional flowing particles */}
        <circle className="particle" r="2" fill="rgba(0, 212, 255, 0.6)" filter="url(#glow)">
          <animateMotion dur="12s" repeatCount="indefinite" begin="2s">
            <mpath xlinkHref="#mainPath1" />
          </animateMotion>
        </circle>
        
        <circle className="particle" r="2.5" fill="rgba(57, 255, 20, 0.6)" filter="url(#glow)">
          <animateMotion dur="9s" repeatCount="indefinite" begin="1.5s">
            <mpath xlinkHref="#mainPath2" />
          </animateMotion>
        </circle>

        {/* Pulsing Node Points */}
        <circle cx="400" cy="300" r="8" fill="rgba(0, 212, 255, 0.5)" filter="url(#strongGlow)">
          <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite" />
          <animate attributeName="opacity" values="0.5;0.8;0.5" dur="2s" repeatCount="indefinite" />
        </circle>
        
        <circle cx="800" cy="250" r="6" fill="rgba(57, 255, 20, 0.5)" filter="url(#strongGlow)">
          <animate attributeName="r" values="6;10;6" dur="1.8s" repeatCount="indefinite" />
          <animate attributeName="opacity" values="0.5;0.8;0.5" dur="1.8s" repeatCount="indefinite" />
        </circle>
        
        <circle cx="1000" cy="600" r="7" fill="rgba(139, 69, 255, 0.5)" filter="url(#strongGlow)">
          <animate attributeName="r" values="7;11;7" dur="2.2s" repeatCount="indefinite" />
          <animate attributeName="opacity" values="0.5;0.8;0.5" dur="2.2s" repeatCount="indefinite" />
        </circle>

        {/* Background Grid Effect */}
        <defs>
          <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
            <path d="M 100 0 L 0 0 0 100" fill="none" stroke="rgba(100, 116, 139, 0.1)" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" className="opacity-30" />
      </svg>
    </div>
  );
}
'use client';

import { useEffect, useRef, useState } from 'react';

interface AnimatedCounterProps {
  end: number;
  duration?: number;
  start?: number;
}

export function AnimatedCounter({ end, duration = 2, start = 0 }: AnimatedCounterProps) {
  const [count, setCount] = useState(start);
  const countRef = useRef(start);
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    const increment = (end - start) / (duration * 60); // 60fps
    const timer = setInterval(() => {
      countRef.current += increment;
      if (countRef.current >= end) {
        countRef.current = end;
        clearInterval(timer);
      }
      setCount(Math.floor(countRef.current));
    }, 1000 / 60);

    return () => clearInterval(timer);
  }, [isVisible, end, start, duration]);

  return <span ref={elementRef}>{count}</span>;
}
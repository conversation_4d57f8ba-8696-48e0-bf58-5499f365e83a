import { z } from "zod";

type ClientEnv = ReturnType<typeof clientEnvironment>;
type ClientEnvVariable = keyof ClientEnv;

const clientEnvironmentSchema = z.object({
  NODE_ENV: z.enum(["development", "test", "production"]).default("development"),
  // Add client-side accessible environment variables here
  // These should be prefixed with NEXT_PUBLIC_ in your .env file
  NEXT_PUBLIC_BASE_URL: z.string().optional(),
  NEXT_PUBLIC_CMS_BASE_URL: z.string().optional(),
});

const clientEnvironment = () => {
  // For client-side, we need to access environment variables differently
  const env = {
    NODE_ENV: process.env.NODE_ENV || "development",
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
    NEXT_PUBLIC_CMS_BASE_URL: process.env.NEXT_PUBLIC_CMS_BASE_URL,
  };
  
  return clientEnvironmentSchema.parse(env);
};

const clientEnv = <K extends ClientEnvVariable>(key: K): ClientEnv[K] => clientEnvironment()[key];

const isProduction = () => clientEnv("NODE_ENV") === "production";

export { clientEnv, clientEnvironment, isProduction };

import { payloadClient } from '@/cms/PayloadCMSClient';

export interface CMSHealthCheck {
  isHealthy: boolean;
  status: string;
  message: string;
  error?: string;
  responseTime?: number;
}

export interface CMSConfig {
  baseUrl: string;
  userSlug: string;
  timeout: number;
  hasApiKey: boolean;
}

/**
 * Check if the CMS is healthy and responding
 */
export async function checkCMSHealth(): Promise<CMSHealthCheck> {
  const startTime = Date.now();
  
  try {
    const result = await payloadClient.healthCheck();
    const responseTime = Date.now() - startTime;
    
    return {
      isHealthy: result.status === 'ok',
      status: result.status,
      message: result.message,
      responseTime
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return {
      isHealthy: false,
      status: 'error',
      message: 'CMS health check failed',
      error: errorMessage,
      responseTime
    };
  }
}

/**
 * Get the current CMS configuration
 */
export function getCMSConfig(): CMSConfig {
  const config = payloadClient.getConfig();
  
  return {
    baseUrl: config.baseUrl,
    userSlug: config.userSlug,
    timeout: config.timeout,
    hasApiKey: true // We don't expose the actual key for security
  };
}

/**
 * Test a simple connection to the CMS without authentication
 */
export async function testCMSConnection(): Promise<{ success: boolean; error?: string }> {
  try {
    const config = getCMSConfig();
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    const response = await fetch(`${config.baseUrl}/api/users?limit=1`, {
      method: 'HEAD', // Just check if the endpoint exists
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    
    return {
      success: response.status < 500, // Even 401/403 means the server is responding
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

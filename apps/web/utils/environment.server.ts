import { z } from "zod";

type Env = ReturnType<typeof environment>;
type EnvVariable = keyof Env;

const environmentSchema = z.object({
  BASE_URL: z.string().min(1),
  CMS_BASE_URL: z.string().min(1),
  CMS_SECRET: z.string().min(1),
  NODE_ENV: z.enum(["development", "test", "production"]).default("development"),
});

const environment = () => environmentSchema.parse(process.env);

const env = <K extends EnvVariable>(key: K): Env[K] => environment()[key];

const isProduction = () => env("NODE_ENV") === "production";

export { env, environment, isProduction };

{"name": "@autopiloot/web", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start", "check-types": "tsc --noEmit", "cms:generate": "npx openapi-typescript http://localhost:3000/api/openapi.json -o ./cms/schema.d.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "1.2.12", "@radix-ui/react-alert-dialog": "1.1.15", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.3", "@radix-ui/react-collapsible": "1.1.12", "@radix-ui/react-context-menu": "2.2.16", "@radix-ui/react-dialog": "1.1.15", "@radix-ui/react-dropdown-menu": "2.1.16", "@radix-ui/react-hover-card": "1.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-menubar": "1.1.16", "@radix-ui/react-navigation-menu": "1.2.14", "@radix-ui/react-popover": "1.1.15", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.8", "@radix-ui/react-scroll-area": "1.2.10", "@radix-ui/react-select": "2.2.6", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.6", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.6", "@radix-ui/react-tabs": "1.1.13", "@radix-ui/react-toast": "1.2.15", "@radix-ui/react-toggle": "1.1.10", "@radix-ui/react-toggle-group": "1.1.11", "@radix-ui/react-tooltip": "1.2.8", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "embla-carousel-react": "8.6.0", "geist": "^1.4.2", "input-otp": "1.4.2", "lucide-react": "^0.540.0", "next": "15.5.0", "next-themes": "^0.4.6", "react": "^19", "react-day-picker": "9.9.0", "react-dom": "^19", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.4", "recharts": "3.1.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "4.0.17", "next-intl": "latest"}, "devDependencies": {"@autopiloot/eslint-config": "workspace:*", "@autopiloot/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "tailwindcss": "^4.1.12", "tw-animate-css": "1.3.7", "typescript": "^5", "openapi-typescript": "^7.9.1"}}